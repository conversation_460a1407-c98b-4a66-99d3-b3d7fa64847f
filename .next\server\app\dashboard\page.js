/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Chackothon%5CBlockchain%5CVintrek%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Chackothon%5CBlockchain%5CVintrek&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Chackothon%5CBlockchain%5CVintrek%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Chackothon%5CBlockchain%5CVintrek&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Chackothon%5CBlockchain%5CVintrek%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Chackothon%5CBlockchain%5CVintrek&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ClientProviders.tsx */ \"(rsc)/./src/components/providers/ClientProviders.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNoYWNrb3Rob24lNUMlNUNCbG9ja2NoYWluJTVDJTVDVmludHJlayU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDaGFja290aG9uJTVDJTVDQmxvY2tjaGFpbiU1QyU1Q1ZpbnRyZWslNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDaGFja290aG9uJTVDJTVDQmxvY2tjaGFpbiU1QyU1Q1ZpbnRyZWslNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDcHJvdmlkZXJzJTVDJTVDQ2xpZW50UHJvdmlkZXJzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkNsaWVudFByb3ZpZGVycyUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd01BQTZKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJDbGllbnRQcm92aWRlcnNcIl0gKi8gXCJEOlxcXFxoYWNrb3Rob25cXFxcQmxvY2tjaGFpblxcXFxWaW50cmVrXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHByb3ZpZGVyc1xcXFxDbGllbnRQcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNoYWNrb3Rob24lNUMlNUNCbG9ja2NoYWluJTVDJTVDVmludHJlayU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvS0FBc0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGhhY2tvdGhvblxcXFxCbG9ja2NoYWluXFxcXFZpbnRyZWtcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\hackothon\\Blockchain\\Vintrek\\src\\app\\dashboard\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"092916a0a569\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxCbG9ja2NoYWluXFxWaW50cmVrXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwOTI5MTZhMGE1NjlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_ClientProviders__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/ClientProviders */ \"(rsc)/./src/components/providers/ClientProviders.tsx\");\n\n\n\n\nconst metadata = {\n    title: 'VinTrek - Blockchain-Powered Eco-Tourism',\n    description: 'Discover, book, and earn rewards for hiking and camping adventures in Sri Lanka. Mint NFTs, earn TREK tokens, and unlock AR/VR experiences.',\n    keywords: 'blockchain, eco-tourism, hiking, camping, NFT, Cardano, Sri Lanka, trails',\n    authors: [\n        {\n            name: 'VinTrek Team'\n        }\n    ],\n    openGraph: {\n        title: 'VinTrek - Blockchain-Powered Eco-Tourism',\n        description: 'Discover trails, mint NFTs, and earn rewards for your outdoor adventures',\n        type: 'website',\n        locale: 'en_US'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_ClientProviders__WEBPACK_IMPORTED_MODULE_2__.ClientProviders, {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers/ClientProviders.tsx":
/*!******************************************************!*\
  !*** ./src/components/providers/ClientProviders.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ClientProviders: () => (/* binding */ ClientProviders)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ClientProviders = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ClientProviders() from the server but ClientProviders is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\hackothon\\Blockchain\\Vintrek\\src\\components\\providers\\ClientProviders.tsx",
"ClientProviders",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ClientProviders.tsx */ \"(ssr)/./src/components/providers/ClientProviders.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNoYWNrb3Rob24lNUMlNUNCbG9ja2NoYWluJTVDJTVDVmludHJlayU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDaGFja290aG9uJTVDJTVDQmxvY2tjaGFpbiU1QyU1Q1ZpbnRyZWslNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDaGFja290aG9uJTVDJTVDQmxvY2tjaGFpbiU1QyU1Q1ZpbnRyZWslNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDcHJvdmlkZXJzJTVDJTVDQ2xpZW50UHJvdmlkZXJzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkNsaWVudFByb3ZpZGVycyUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd01BQTZKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJDbGllbnRQcm92aWRlcnNcIl0gKi8gXCJEOlxcXFxoYWNrb3Rob25cXFxcQmxvY2tjaGFpblxcXFxWaW50cmVrXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHByb3ZpZGVyc1xcXFxDbGllbnRQcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNoYWNrb3Rob24lNUMlNUNCbG9ja2NoYWluJTVDJTVDVmludHJlayU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvS0FBc0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGhhY2tvdGhvblxcXFxCbG9ja2NoYWluXFxcXFZpbnRyZWtcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Coins,MapPin,Mountain,TrendingUp,Trophy!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Coins,MapPin,Mountain,TrendingUp,Trophy!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mountain.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Coins,MapPin,Mountain,TrendingUp,Trophy!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Coins,MapPin,Mountain,TrendingUp,Trophy!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Coins,MapPin,Mountain,TrendingUp,Trophy!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trophy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Coins,MapPin,Mountain,TrendingUp,Trophy!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/coins.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/providers/WalletProvider */ \"(ssr)/./src/components/providers/WalletProvider.tsx\");\n/* harmony import */ var _hooks_useTrailData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useTrailData */ \"(ssr)/./src/hooks/useTrailData.ts\");\n/* harmony import */ var _lib_trailUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/trailUtils */ \"(ssr)/./src/lib/trailUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction DashboardPage() {\n    const { connected } = (0,_components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_3__.useWallet)();\n    const { completedTrails, userStats, recordings } = (0,_hooks_useTrailData__WEBPACK_IMPORTED_MODULE_4__.useTrailData)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    if (!connected) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-green-50 to-blue-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"bg-white shadow-sm border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center h-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 27,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"VinTrek\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto px-4 py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-lg p-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-16 w-16 text-gray-300 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                children: \"Wallet Required\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: \"Please connect your Cardano wallet to view your trail dashboard.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors inline-block\",\n                                children: \"Connect Wallet\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-50 to-blue-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"VinTrek\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: \"Trail Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Track your progress and view your hiking achievements\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-8 px-6\",\n                                    children: [\n                                        {\n                                            id: 'overview',\n                                            label: 'Overview',\n                                            icon: _barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                                        },\n                                        {\n                                            id: 'trails',\n                                            label: 'Completed Trails',\n                                            icon: _barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                                        },\n                                        {\n                                            id: 'recordings',\n                                            label: 'Recordings',\n                                            icon: _barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                                        },\n                                        {\n                                            id: 'achievements',\n                                            label: 'Achievements',\n                                            icon: _barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                                        }\n                                    ].map(({ id, label, icon: Icon })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setActiveTab(id),\n                                            className: `flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm ${activeTab === id ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: label\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, id, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 md:grid-cols-4 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 115,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-green-800\",\n                                                                        children: \"Total Trails\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 116,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 114,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-green-900\",\n                                                                children: userStats.totalTrails\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 118,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 123,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-blue-800\",\n                                                                        children: \"Distance\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 124,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 122,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-blue-900\",\n                                                                children: (0,_lib_trailUtils__WEBPACK_IMPORTED_MODULE_5__.formatDistance)(userStats.totalDistance)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-purple-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 133,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-purple-800\",\n                                                                        children: \"Time\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 134,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 132,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-purple-900\",\n                                                                children: (0,_lib_trailUtils__WEBPACK_IMPORTED_MODULE_5__.formatDuration)(userStats.totalDuration)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 136,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-yellow-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 143,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-yellow-800\",\n                                                                        children: \"TREK Tokens\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 144,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-yellow-900\",\n                                                                children: userStats.trekTokensEarned\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 146,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                        children: \"Recent Trails\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    completedTrails.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8 text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"No completed trails yet\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/record\",\n                                                                className: \"text-green-600 hover:text-green-700 font-medium\",\n                                                                children: \"Start your first trail →\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: completedTrails.slice(0, 5).map((trail)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-50 rounded-lg p-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: trail.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 169,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: (0,_lib_trailUtils__WEBPACK_IMPORTED_MODULE_5__.formatDistance)(trail.distance)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                        lineNumber: 171,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: (0,_lib_trailUtils__WEBPACK_IMPORTED_MODULE_5__.formatDuration)(trail.duration)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                        lineNumber: 172,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"capitalize\",\n                                                                                        children: trail.difficulty\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                        lineNumber: 173,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 170,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 168,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: new Date(trail.completedAt).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 176,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, trail.id, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeTab === 'trails' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                children: [\n                                                    \"Completed Trails (\",\n                                                    completedTrails.length,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this),\n                                            completedTrails.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-12 text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-16 w-16 mx-auto mb-4 text-gray-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg mb-2\",\n                                                        children: \"No completed trails yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mb-4\",\n                                                        children: \"Start recording your first trail to see it here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/record\",\n                                                        className: \"bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors inline-block\",\n                                                        children: \"Record Trail\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-4\",\n                                                children: completedTrails.map((trail)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 rounded-lg p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start justify-between mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-lg font-semibold text-gray-900\",\n                                                                                children: trail.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 210,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: trail.location\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 211,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 209,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: `px-2 py-1 rounded text-xs font-medium ${trail.difficulty === 'Easy' ? 'bg-green-100 text-green-800' : trail.difficulty === 'Moderate' ? 'bg-yellow-100 text-yellow-800' : trail.difficulty === 'Hard' ? 'bg-orange-100 text-orange-800' : 'bg-red-100 text-red-800'}`,\n                                                                                children: trail.difficulty\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 214,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            trail.nftMinted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs font-medium\",\n                                                                                children: \"NFT Minted\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 223,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 213,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Distance\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 232,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: (0,_lib_trailUtils__WEBPACK_IMPORTED_MODULE_5__.formatDistance)(trail.distance)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 233,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 231,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Duration\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 236,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: (0,_lib_trailUtils__WEBPACK_IMPORTED_MODULE_5__.formatDuration)(trail.duration)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 237,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 235,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"TREK Tokens\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 240,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: trail.trekTokensEarned\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 241,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 239,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Completed\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 244,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: new Date(trail.completedAt).toLocaleDateString()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 245,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            trail.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 p-3 bg-white rounded border\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: \"Notes\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 253,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm\",\n                                                                        children: trail.notes\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 254,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, trail.id, true, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeTab === 'recordings' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                children: [\n                                                    \"Trail Recordings (\",\n                                                    recordings.length,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this),\n                                            recordings.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-12 text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-16 w-16 mx-auto mb-4 text-gray-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg mb-2\",\n                                                        children: \"No recordings found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mb-4\",\n                                                        children: \"Your trail recordings will appear here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-4\",\n                                                children: recordings.map((recording)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 rounded-lg p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start justify-between mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-lg font-semibold text-gray-900\",\n                                                                                children: recording.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 281,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            recording.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: recording.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 283,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: `px-2 py-1 rounded text-xs font-medium ${recording.isActive ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}`,\n                                                                            children: recording.isActive ? 'Active' : 'Completed'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 287,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Distance\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 298,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: (0,_lib_trailUtils__WEBPACK_IMPORTED_MODULE_5__.formatDistance)(recording.totalDistance)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 299,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 297,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Duration\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 302,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: (0,_lib_trailUtils__WEBPACK_IMPORTED_MODULE_5__.formatDuration)(recording.totalDuration)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 303,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Points\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 306,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: recording.coordinates.length\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 307,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Started\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 310,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: new Date(recording.startTime).toLocaleDateString()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 311,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 309,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, recording.id, true, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeTab === 'achievements' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                children: [\n                                                    \"Achievements (\",\n                                                    userStats.achievements.length,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this),\n                                            userStats.achievements.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-12 text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-16 w-16 mx-auto mb-4 text-gray-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg mb-2\",\n                                                        children: \"No achievements yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mb-4\",\n                                                        children: \"Complete trails to unlock achievements\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid md:grid-cols-2 gap-4\",\n                                                children: userStats.achievements.map((achievement)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg p-4 border border-yellow-200\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 bg-yellow-200 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-6 w-6 text-yellow-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-semibold text-yellow-900\",\n                                                                            children: achievement.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 343,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-yellow-700\",\n                                                                            children: achievement.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 344,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-yellow-600 capitalize\",\n                                                                            children: achievement.category\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 345,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, achievement.id, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/ClientProviders.tsx":
/*!******************************************************!*\
  !*** ./src/components/providers/ClientProviders.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientProviders: () => (/* binding */ ClientProviders)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _WalletProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./WalletProvider */ \"(ssr)/./src/components/providers/WalletProvider.tsx\");\n/* harmony import */ var _components_ui_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ErrorBoundary */ \"(ssr)/./src/components/ui/ErrorBoundary.tsx\");\n/* __next_internal_client_entry_do_not_use__ ClientProviders auto */ \n\n\nfunction ClientProviders({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__.ErrorBoundary, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WalletProvider__WEBPACK_IMPORTED_MODULE_1__.WalletProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\providers\\\\ClientProviders.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\providers\\\\ClientProviders.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvQ2xpZW50UHJvdmlkZXJzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFHaUQ7QUFDWTtBQU10RCxTQUFTRSxnQkFBZ0IsRUFBRUMsUUFBUSxFQUF3QjtJQUNoRSxxQkFDRSw4REFBQ0YsdUVBQWFBO2tCQUNaLDRFQUFDRCwyREFBY0E7c0JBQ1pHOzs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxCbG9ja2NoYWluXFxWaW50cmVrXFxzcmNcXGNvbXBvbmVudHNcXHByb3ZpZGVyc1xcQ2xpZW50UHJvdmlkZXJzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBXYWxsZXRQcm92aWRlciB9IGZyb20gJy4vV2FsbGV0UHJvdmlkZXInXG5pbXBvcnQgeyBFcnJvckJvdW5kYXJ5IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL0Vycm9yQm91bmRhcnknXG5cbmludGVyZmFjZSBDbGllbnRQcm92aWRlcnNQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIENsaWVudFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IENsaWVudFByb3ZpZGVyc1Byb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPEVycm9yQm91bmRhcnk+XG4gICAgICA8V2FsbGV0UHJvdmlkZXI+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvV2FsbGV0UHJvdmlkZXI+XG4gICAgPC9FcnJvckJvdW5kYXJ5PlxuICApXG59XG4iXSwibmFtZXMiOlsiV2FsbGV0UHJvdmlkZXIiLCJFcnJvckJvdW5kYXJ5IiwiQ2xpZW50UHJvdmlkZXJzIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/ClientProviders.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/WalletProvider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/WalletProvider.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletProvider: () => (/* binding */ WalletProvider),\n/* harmony export */   useWallet: () => (/* binding */ useWallet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useWallet,WalletProvider auto */ \n\nconst WalletContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useWallet() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(WalletContext);\n    if (context === undefined) {\n        throw new Error('useWallet must be used within a WalletProvider');\n    }\n    return context;\n}\nfunction WalletProvider({ children }) {\n    const [wallet, setWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connected, setConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [connecting, setConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [address, setAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [balance, setBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getAvailableWallets = ()=>{\n        if (true) return [];\n        const wallets = [];\n        if (window.cardano?.lace) wallets.push('lace');\n        if (window.cardano?.eternl) wallets.push('eternl');\n        if (window.cardano?.nami) wallets.push('nami');\n        if (window.cardano?.flint) wallets.push('flint');\n        return wallets;\n    };\n    const connect = async (walletName)=>{\n        if (true) return;\n        setConnecting(true);\n        try {\n            // Check if wallet is available\n            if (!window.cardano || !window.cardano[walletName]) {\n                throw new Error(`${walletName} wallet not found`);\n            }\n            // Enable the wallet\n            const walletApi = await window.cardano[walletName].enable();\n            setWallet(walletApi);\n            // Get wallet address\n            try {\n                const addresses = await walletApi.getUsedAddresses();\n                if (addresses.length > 0) {\n                    setAddress(addresses[0]);\n                }\n            } catch (error) {\n                console.warn('Could not fetch addresses:', error);\n            }\n            // Get wallet balance\n            try {\n                const balance = await walletApi.getBalance();\n                setBalance(balance);\n            } catch (error) {\n                console.warn('Could not fetch balance:', error);\n                setBalance('0');\n            }\n            setConnected(true);\n            // Store connection state\n            localStorage.setItem('vintrek_wallet', walletName);\n        } catch (error) {\n            console.error('Failed to connect wallet:', error);\n            throw error;\n        } finally{\n            setConnecting(false);\n        }\n    };\n    const disconnect = ()=>{\n        setWallet(null);\n        setConnected(false);\n        setAddress(null);\n        setBalance(null);\n        localStorage.removeItem('vintrek_wallet');\n    };\n    // Auto-reconnect on page load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletProvider.useEffect\": ()=>{\n            const savedWallet = localStorage.getItem('vintrek_wallet');\n            if (savedWallet && getAvailableWallets().includes(savedWallet)) {\n                connect(savedWallet).catch(console.error);\n            }\n        }\n    }[\"WalletProvider.useEffect\"], []);\n    const value = {\n        wallet,\n        connected,\n        connecting,\n        address,\n        balance,\n        connect,\n        disconnect,\n        getAvailableWallets\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\providers\\\\WalletProvider.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/WalletProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ErrorBoundary.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/ErrorBoundary.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),\n/* harmony export */   useErrorHandler: () => (/* binding */ useErrorHandler)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.mjs\");\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary,useErrorHandler auto */ \n\n\nclass ErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props), this.resetError = ()=>{\n            this.setState({\n                hasError: false,\n                error: undefined\n            });\n        };\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error('ErrorBoundary caught an error:', error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                const FallbackComponent = this.props.fallback;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FallbackComponent, {\n                    error: this.state.error,\n                    resetError: this.resetError\n                }, void 0, false, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 16\n                }, this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DefaultErrorFallback, {\n                error: this.state.error,\n                resetError: this.resetError\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                lineNumber: 41,\n                columnNumber: 14\n            }, this);\n        }\n        return this.props.children;\n    }\n}\nfunction DefaultErrorFallback({ error, resetError }) {\n    const isBlockchainError = error?.message?.includes('blockchain') || error?.message?.includes('wallet') || error?.message?.includes('WASM');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"h-16 w-16 text-red-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: isBlockchainError ? 'Blockchain Connection Error' : 'Something went wrong'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: isBlockchainError ? 'There was an issue connecting to the blockchain services. This might be due to network issues or browser compatibility.' : 'An unexpected error occurred. Please try refreshing the page.'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                        className: \"mb-6 text-left\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                className: \"cursor-pointer text-sm text-gray-500 hover:text-gray-700\",\n                                children: \"Technical details\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-32\",\n                                children: error.message\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: resetError,\n                                className: \"w-full flex items-center justify-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Try Again\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.location.href = '/',\n                                className: \"w-full flex items-center justify-center space-x-2 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Go Home\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this),\n                    isBlockchainError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 bg-blue-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-blue-800 mb-2\",\n                                children: \"Troubleshooting Tips:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-xs text-blue-700 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Make sure you're using a modern browser (Chrome, Firefox, Edge)\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Check your internet connection\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Try refreshing the page\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Clear your browser cache if the issue persists\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n// Hook for functional components\nfunction useErrorHandler() {\n    return (error)=>{\n        console.error('Error caught by useErrorHandler:', error);\n    // You could also send this to an error reporting service\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useTrailData.ts":
/*!***********************************!*\
  !*** ./src/hooks/useTrailData.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTrailData: () => (/* binding */ useTrailData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/trailStorage */ \"(ssr)/./src/lib/trailStorage.ts\");\n/* harmony import */ var _components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/WalletProvider */ \"(ssr)/./src/components/providers/WalletProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ useTrailData auto */ \n\n\nfunction useTrailData() {\n    const { address } = (0,_components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__.useWallet)();\n    const [recordings, setRecordings] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [completedTrails, setCompletedTrails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [userStats, setUserStats] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        totalTrails: 0,\n        totalDistance: 0,\n        totalDuration: 0,\n        totalElevationGain: 0,\n        trekTokensEarned: 0,\n        nftsMinted: 0,\n        achievements: []\n    });\n    const [storageUsage, setStorageUsage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        used: 0,\n        total: 0,\n        percentage: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Load data from storage\n    const loadData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTrailData.useCallback[loadData]\": ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                // Load recordings\n                const loadedRecordings = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.getTrailRecordings();\n                setRecordings(loadedRecordings);\n                // Load completed trails for current user\n                if (address) {\n                    const loadedTrails = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.getCompletedTrails(address);\n                    setCompletedTrails(loadedTrails);\n                    const loadedStats = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.getUserStats(address);\n                    setUserStats(loadedStats);\n                } else {\n                    setCompletedTrails([]);\n                    setUserStats({\n                        totalTrails: 0,\n                        totalDistance: 0,\n                        totalDuration: 0,\n                        totalElevationGain: 0,\n                        trekTokensEarned: 0,\n                        nftsMinted: 0,\n                        achievements: []\n                    });\n                }\n                // Load storage usage\n                const usage = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.getStorageUsage();\n                setStorageUsage(usage);\n            } catch (err) {\n                setError(err instanceof Error ? err.message : 'Failed to load trail data');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useTrailData.useCallback[loadData]\"], [\n        address\n    ]);\n    // Save recording\n    const saveRecording = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTrailData.useCallback[saveRecording]\": (recording)=>{\n            try {\n                const success = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.saveTrailRecording(recording);\n                if (success) {\n                    setRecordings({\n                        \"useTrailData.useCallback[saveRecording]\": (prev)=>{\n                            const existing = prev.findIndex({\n                                \"useTrailData.useCallback[saveRecording].existing\": (r)=>r.id === recording.id\n                            }[\"useTrailData.useCallback[saveRecording].existing\"]);\n                            if (existing >= 0) {\n                                const updated = [\n                                    ...prev\n                                ];\n                                updated[existing] = recording;\n                                return updated;\n                            } else {\n                                return [\n                                    ...prev,\n                                    recording\n                                ];\n                            }\n                        }\n                    }[\"useTrailData.useCallback[saveRecording]\"]);\n                    // Update storage usage\n                    setStorageUsage(_lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.getStorageUsage());\n                }\n                return success;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : 'Failed to save recording');\n                return false;\n            }\n        }\n    }[\"useTrailData.useCallback[saveRecording]\"], []);\n    // Delete recording\n    const deleteRecording = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTrailData.useCallback[deleteRecording]\": (id)=>{\n            try {\n                const success = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.deleteTrailRecording(id);\n                if (success) {\n                    setRecordings({\n                        \"useTrailData.useCallback[deleteRecording]\": (prev)=>prev.filter({\n                                \"useTrailData.useCallback[deleteRecording]\": (r)=>r.id !== id\n                            }[\"useTrailData.useCallback[deleteRecording]\"])\n                    }[\"useTrailData.useCallback[deleteRecording]\"]);\n                    setStorageUsage(_lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.getStorageUsage());\n                }\n                return success;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : 'Failed to delete recording');\n                return false;\n            }\n        }\n    }[\"useTrailData.useCallback[deleteRecording]\"], []);\n    // Get recording by ID\n    const getRecording = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTrailData.useCallback[getRecording]\": (id)=>{\n            return recordings.find({\n                \"useTrailData.useCallback[getRecording]\": (r)=>r.id === id\n            }[\"useTrailData.useCallback[getRecording]\"]) || null;\n        }\n    }[\"useTrailData.useCallback[getRecording]\"], [\n        recordings\n    ]);\n    // Save completed trail\n    const saveCompletedTrail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTrailData.useCallback[saveCompletedTrail]\": (trail)=>{\n            if (!address) {\n                setError('Wallet not connected');\n                return false;\n            }\n            try {\n                const trailWithAddress = {\n                    ...trail,\n                    walletAddress: address\n                };\n                const success = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.saveCompletedTrail(trailWithAddress);\n                if (success) {\n                    setCompletedTrails({\n                        \"useTrailData.useCallback[saveCompletedTrail]\": (prev)=>{\n                            const existing = prev.findIndex({\n                                \"useTrailData.useCallback[saveCompletedTrail].existing\": (t)=>t.id === trail.id\n                            }[\"useTrailData.useCallback[saveCompletedTrail].existing\"]);\n                            if (existing >= 0) {\n                                const updated = [\n                                    ...prev\n                                ];\n                                updated[existing] = trailWithAddress;\n                                return updated;\n                            } else {\n                                return [\n                                    ...prev,\n                                    trailWithAddress\n                                ];\n                            }\n                        }\n                    }[\"useTrailData.useCallback[saveCompletedTrail]\"]);\n                    // Refresh stats\n                    const updatedStats = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.getUserStats(address);\n                    setUserStats(updatedStats);\n                    // Update storage usage\n                    setStorageUsage(_lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.getStorageUsage());\n                }\n                return success;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : 'Failed to save completed trail');\n                return false;\n            }\n        }\n    }[\"useTrailData.useCallback[saveCompletedTrail]\"], [\n        address\n    ]);\n    // Get completed trail by ID\n    const getCompletedTrail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTrailData.useCallback[getCompletedTrail]\": (id)=>{\n            return completedTrails.find({\n                \"useTrailData.useCallback[getCompletedTrail]\": (t)=>t.id === id\n            }[\"useTrailData.useCallback[getCompletedTrail]\"]) || null;\n        }\n    }[\"useTrailData.useCallback[getCompletedTrail]\"], [\n        completedTrails\n    ]);\n    // Refresh stats\n    const refreshStats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTrailData.useCallback[refreshStats]\": ()=>{\n            if (address) {\n                const stats = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.getUserStats(address);\n                setUserStats(stats);\n            }\n        }\n    }[\"useTrailData.useCallback[refreshStats]\"], [\n        address\n    ]);\n    // Export data\n    const exportData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTrailData.useCallback[exportData]\": ()=>{\n            if (!address) {\n                throw new Error('Wallet not connected');\n            }\n            return _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.exportTrailData(address);\n        }\n    }[\"useTrailData.useCallback[exportData]\"], [\n        address\n    ]);\n    // Import data\n    const importData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTrailData.useCallback[importData]\": (jsonData)=>{\n            try {\n                const success = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.importTrailData(jsonData);\n                if (success) {\n                    loadData() // Reload all data\n                    ;\n                }\n                return success;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : 'Failed to import data');\n                return false;\n            }\n        }\n    }[\"useTrailData.useCallback[importData]\"], [\n        loadData\n    ]);\n    // Clear all data\n    const clearAllData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTrailData.useCallback[clearAllData]\": ()=>{\n            try {\n                const success = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.clearAllData();\n                if (success) {\n                    setRecordings([]);\n                    setCompletedTrails([]);\n                    setUserStats({\n                        totalTrails: 0,\n                        totalDistance: 0,\n                        totalDuration: 0,\n                        totalElevationGain: 0,\n                        trekTokensEarned: 0,\n                        nftsMinted: 0,\n                        achievements: []\n                    });\n                    setStorageUsage({\n                        used: 0,\n                        total: 0,\n                        percentage: 0\n                    });\n                }\n                return success;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : 'Failed to clear data');\n                return false;\n            }\n        }\n    }[\"useTrailData.useCallback[clearAllData]\"], []);\n    // Load data when component mounts or address changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTrailData.useEffect\": ()=>{\n            loadData();\n        }\n    }[\"useTrailData.useEffect\"], [\n        loadData\n    ]);\n    return {\n        recordings,\n        saveRecording,\n        deleteRecording,\n        getRecording,\n        completedTrails,\n        saveCompletedTrail,\n        getCompletedTrail,\n        userStats,\n        refreshStats,\n        exportData,\n        importData,\n        clearAllData,\n        storageUsage,\n        loading,\n        error\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useTrailData.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/trailStorage.ts":
/*!*********************************!*\
  !*** ./src/lib/trailStorage.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrailStorage: () => (/* binding */ TrailStorage)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ TrailStorage auto */ // Storage keys\nconst STORAGE_KEYS = {\n    TRAIL_RECORDINGS: 'vintrek_trail_recordings',\n    COMPLETED_TRAILS: 'vintrek_completed_trails',\n    USER_STATS: 'vintrek_user_stats',\n    TRAIL_TEMPLATES: 'vintrek_trail_templates',\n    OFFLINE_DATA: 'vintrek_offline_data'\n};\n// Trail Recording Storage\nclass TrailStorage {\n    // Save trail recording\n    static saveTrailRecording(recording) {\n        try {\n            const recordings = this.getTrailRecordings();\n            const existingIndex = recordings.findIndex((r)=>r.id === recording.id);\n            if (existingIndex >= 0) {\n                recordings[existingIndex] = recording;\n            } else {\n                recordings.push(recording);\n            }\n            localStorage.setItem(STORAGE_KEYS.TRAIL_RECORDINGS, JSON.stringify(recordings));\n            return true;\n        } catch (error) {\n            console.error('Failed to save trail recording:', error);\n            return false;\n        }\n    }\n    // Get all trail recordings\n    static getTrailRecordings() {\n        try {\n            const data = localStorage.getItem(STORAGE_KEYS.TRAIL_RECORDINGS);\n            return data ? JSON.parse(data) : [];\n        } catch (error) {\n            console.error('Failed to get trail recordings:', error);\n            return [];\n        }\n    }\n    // Get trail recording by ID\n    static getTrailRecording(id) {\n        const recordings = this.getTrailRecordings();\n        return recordings.find((r)=>r.id === id) || null;\n    }\n    // Delete trail recording\n    static deleteTrailRecording(id) {\n        try {\n            const recordings = this.getTrailRecordings();\n            const filtered = recordings.filter((r)=>r.id !== id);\n            localStorage.setItem(STORAGE_KEYS.TRAIL_RECORDINGS, JSON.stringify(filtered));\n            return true;\n        } catch (error) {\n            console.error('Failed to delete trail recording:', error);\n            return false;\n        }\n    }\n    // Save completed trail\n    static saveCompletedTrail(trail) {\n        try {\n            const trails = this.getCompletedTrails();\n            const existingIndex = trails.findIndex((t)=>t.id === trail.id);\n            if (existingIndex >= 0) {\n                trails[existingIndex] = trail;\n            } else {\n                trails.push(trail);\n            }\n            localStorage.setItem(STORAGE_KEYS.COMPLETED_TRAILS, JSON.stringify(trails));\n            this.updateUserStats(trail);\n            return true;\n        } catch (error) {\n            console.error('Failed to save completed trail:', error);\n            return false;\n        }\n    }\n    // Get completed trails\n    static getCompletedTrails(walletAddress) {\n        try {\n            const data = localStorage.getItem(STORAGE_KEYS.COMPLETED_TRAILS);\n            const trails = data ? JSON.parse(data) : [];\n            if (walletAddress) {\n                return trails.filter((t)=>t.walletAddress === walletAddress);\n            }\n            return trails;\n        } catch (error) {\n            console.error('Failed to get completed trails:', error);\n            return [];\n        }\n    }\n    // Get completed trail by ID\n    static getCompletedTrail(id) {\n        const trails = this.getCompletedTrails();\n        return trails.find((t)=>t.id === id) || null;\n    }\n    // Update user statistics\n    static updateUserStats(completedTrail) {\n        try {\n            const stats = this.getUserStats(completedTrail.walletAddress);\n            const updatedStats = {\n                totalTrails: stats.totalTrails + 1,\n                totalDistance: stats.totalDistance + completedTrail.distance,\n                totalDuration: stats.totalDuration + completedTrail.duration,\n                totalElevationGain: stats.totalElevationGain + completedTrail.coordinates.reduce((gain, coord, index)=>{\n                    if (index === 0 || !coord.altitude) return gain;\n                    const prevCoord = completedTrail.coordinates[index - 1];\n                    if (!prevCoord.altitude) return gain;\n                    const diff = coord.altitude - prevCoord.altitude;\n                    return gain + (diff > 0 ? diff : 0);\n                }, 0),\n                trekTokensEarned: stats.trekTokensEarned + completedTrail.trekTokensEarned,\n                nftsMinted: stats.nftsMinted + (completedTrail.nftMinted ? 1 : 0),\n                favoriteTrail: this.getFavoriteTrail(completedTrail.walletAddress),\n                longestTrail: this.getLongestTrail(completedTrail.walletAddress),\n                achievements: this.calculateAchievements(completedTrail.walletAddress)\n            };\n            localStorage.setItem(`${STORAGE_KEYS.USER_STATS}_${completedTrail.walletAddress}`, JSON.stringify(updatedStats));\n        } catch (error) {\n            console.error('Failed to update user stats:', error);\n        }\n    }\n    // Get user statistics\n    static getUserStats(walletAddress) {\n        try {\n            const data = localStorage.getItem(`${STORAGE_KEYS.USER_STATS}_${walletAddress}`);\n            return data ? JSON.parse(data) : {\n                totalTrails: 0,\n                totalDistance: 0,\n                totalDuration: 0,\n                totalElevationGain: 0,\n                trekTokensEarned: 0,\n                nftsMinted: 0,\n                achievements: []\n            };\n        } catch (error) {\n            console.error('Failed to get user stats:', error);\n            return {\n                totalTrails: 0,\n                totalDistance: 0,\n                totalDuration: 0,\n                totalElevationGain: 0,\n                trekTokensEarned: 0,\n                nftsMinted: 0,\n                achievements: []\n            };\n        }\n    }\n    // Get favorite trail (most completed)\n    static getFavoriteTrail(walletAddress) {\n        const trails = this.getCompletedTrails(walletAddress);\n        const trailCounts = trails.reduce((counts, trail)=>{\n            counts[trail.name] = (counts[trail.name] || 0) + 1;\n            return counts;\n        }, {});\n        return Object.entries(trailCounts).sort(([, a], [, b])=>b - a)[0]?.[0];\n    }\n    // Get longest trail\n    static getLongestTrail(walletAddress) {\n        const trails = this.getCompletedTrails(walletAddress);\n        return trails.sort((a, b)=>b.distance - a.distance)[0]?.name;\n    }\n    // Calculate achievements\n    static calculateAchievements(walletAddress) {\n        const trails = this.getCompletedTrails(walletAddress);\n        const stats = this.getUserStats(walletAddress);\n        const achievements = [];\n        // Distance achievements\n        if (stats.totalDistance >= 100000) achievements.push({\n            id: 'distance_100k',\n            name: '100km Explorer',\n            category: 'distance'\n        });\n        if (stats.totalDistance >= 50000) achievements.push({\n            id: 'distance_50k',\n            name: '50km Adventurer',\n            category: 'distance'\n        });\n        if (stats.totalDistance >= 10000) achievements.push({\n            id: 'distance_10k',\n            name: '10km Hiker',\n            category: 'distance'\n        });\n        // Trail count achievements\n        if (stats.totalTrails >= 50) achievements.push({\n            id: 'trails_50',\n            name: 'Trail Master',\n            category: 'trails'\n        });\n        if (stats.totalTrails >= 20) achievements.push({\n            id: 'trails_20',\n            name: 'Trail Enthusiast',\n            category: 'trails'\n        });\n        if (stats.totalTrails >= 5) achievements.push({\n            id: 'trails_5',\n            name: 'Trail Explorer',\n            category: 'trails'\n        });\n        // Special achievements\n        if (trails.some((t)=>t.difficulty === 'Expert')) achievements.push({\n            id: 'expert_trail',\n            name: 'Expert Climber',\n            category: 'special'\n        });\n        if (stats.nftsMinted >= 10) achievements.push({\n            id: 'nft_collector',\n            name: 'NFT Collector',\n            category: 'special'\n        });\n        return achievements;\n    }\n    // Export trail data\n    static exportTrailData(walletAddress) {\n        const data = {\n            recordings: this.getTrailRecordings(),\n            completedTrails: this.getCompletedTrails(walletAddress),\n            stats: this.getUserStats(walletAddress),\n            exportedAt: new Date().toISOString()\n        };\n        return JSON.stringify(data, null, 2);\n    }\n    // Import trail data\n    static importTrailData(jsonData) {\n        try {\n            const data = JSON.parse(jsonData);\n            if (data.recordings) {\n                localStorage.setItem(STORAGE_KEYS.TRAIL_RECORDINGS, JSON.stringify(data.recordings));\n            }\n            if (data.completedTrails) {\n                localStorage.setItem(STORAGE_KEYS.COMPLETED_TRAILS, JSON.stringify(data.completedTrails));\n            }\n            return true;\n        } catch (error) {\n            console.error('Failed to import trail data:', error);\n            return false;\n        }\n    }\n    // Clear all data\n    static clearAllData() {\n        try {\n            Object.values(STORAGE_KEYS).forEach((key)=>{\n                localStorage.removeItem(key);\n            });\n            // Clear user-specific stats\n            for(let i = 0; i < localStorage.length; i++){\n                const key = localStorage.key(i);\n                if (key?.startsWith(STORAGE_KEYS.USER_STATS)) {\n                    localStorage.removeItem(key);\n                }\n            }\n            return true;\n        } catch (error) {\n            console.error('Failed to clear data:', error);\n            return false;\n        }\n    }\n    // Get storage usage\n    static getStorageUsage() {\n        try {\n            let used = 0;\n            for(let i = 0; i < localStorage.length; i++){\n                const key = localStorage.key(i);\n                if (key?.startsWith('vintrek_')) {\n                    used += localStorage.getItem(key)?.length || 0;\n                }\n            }\n            // Estimate total available (usually 5-10MB)\n            const total = 5 * 1024 * 1024 // 5MB estimate\n            ;\n            const percentage = used / total * 100;\n            return {\n                used,\n                total,\n                percentage\n            };\n        } catch (error) {\n            console.error('Failed to get storage usage:', error);\n            return {\n                used: 0,\n                total: 0,\n                percentage: 0\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/trailStorage.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/trailUtils.ts":
/*!*******************************!*\
  !*** ./src/lib/trailUtils.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateAverageSpeed: () => (/* binding */ calculateAverageSpeed),\n/* harmony export */   calculateDistance: () => (/* binding */ calculateDistance),\n/* harmony export */   calculateElevationGain: () => (/* binding */ calculateElevationGain),\n/* harmony export */   calculateElevationLoss: () => (/* binding */ calculateElevationLoss),\n/* harmony export */   calculateMaxSpeed: () => (/* binding */ calculateMaxSpeed),\n/* harmony export */   calculateTotalDistance: () => (/* binding */ calculateTotalDistance),\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance),\n/* harmony export */   formatDuration: () => (/* binding */ formatDuration),\n/* harmony export */   formatSpeed: () => (/* binding */ formatSpeed),\n/* harmony export */   generateTrailId: () => (/* binding */ generateTrailId),\n/* harmony export */   isValidCoordinate: () => (/* binding */ isValidCoordinate),\n/* harmony export */   isWithinRadius: () => (/* binding */ isWithinRadius),\n/* harmony export */   smoothCoordinates: () => (/* binding */ smoothCoordinates)\n/* harmony export */ });\n/**\n * Calculate distance between two GPS coordinates using Haversine formula\n * @param coord1 First coordinate\n * @param coord2 Second coordinate\n * @returns Distance in meters\n */ function calculateDistance(coord1, coord2) {\n    const R = 6371000 // Earth's radius in meters\n    ;\n    const φ1 = coord1.latitude * Math.PI / 180;\n    const φ2 = coord2.latitude * Math.PI / 180;\n    const Δφ = (coord2.latitude - coord1.latitude) * Math.PI / 180;\n    const Δλ = (coord2.longitude - coord1.longitude) * Math.PI / 180;\n    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) + Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n    return R * c;\n}\n/**\n * Calculate total distance for an array of coordinates\n * @param coordinates Array of GPS coordinates\n * @returns Total distance in meters\n */ function calculateTotalDistance(coordinates) {\n    if (coordinates.length < 2) return 0;\n    let totalDistance = 0;\n    for(let i = 1; i < coordinates.length; i++){\n        totalDistance += calculateDistance(coordinates[i - 1], coordinates[i]);\n    }\n    return totalDistance;\n}\n/**\n * Calculate elevation gain from coordinates\n * @param coordinates Array of GPS coordinates with altitude\n * @returns Elevation gain in meters\n */ function calculateElevationGain(coordinates) {\n    if (coordinates.length < 2) return 0;\n    let elevationGain = 0;\n    for(let i = 1; i < coordinates.length; i++){\n        const prev = coordinates[i - 1];\n        const curr = coordinates[i];\n        if (prev.altitude !== undefined && curr.altitude !== undefined) {\n            const diff = curr.altitude - prev.altitude;\n            if (diff > 0) {\n                elevationGain += diff;\n            }\n        }\n    }\n    return elevationGain;\n}\n/**\n * Calculate elevation loss from coordinates\n * @param coordinates Array of GPS coordinates with altitude\n * @returns Elevation loss in meters\n */ function calculateElevationLoss(coordinates) {\n    if (coordinates.length < 2) return 0;\n    let elevationLoss = 0;\n    for(let i = 1; i < coordinates.length; i++){\n        const prev = coordinates[i - 1];\n        const curr = coordinates[i];\n        if (prev.altitude !== undefined && curr.altitude !== undefined) {\n            const diff = prev.altitude - curr.altitude;\n            if (diff > 0) {\n                elevationLoss += diff;\n            }\n        }\n    }\n    return elevationLoss;\n}\n/**\n * Calculate average speed\n * @param distance Distance in meters\n * @param duration Duration in seconds\n * @returns Average speed in m/s\n */ function calculateAverageSpeed(distance, duration) {\n    if (duration === 0) return 0;\n    return distance / duration;\n}\n/**\n * Calculate maximum speed from coordinates\n * @param coordinates Array of GPS coordinates with timestamps\n * @returns Maximum speed in m/s\n */ function calculateMaxSpeed(coordinates) {\n    if (coordinates.length < 2) return 0;\n    let maxSpeed = 0;\n    for(let i = 1; i < coordinates.length; i++){\n        const prev = coordinates[i - 1];\n        const curr = coordinates[i];\n        const distance = calculateDistance(prev, curr);\n        const timeDiff = (new Date(curr.timestamp).getTime() - new Date(prev.timestamp).getTime()) / 1000;\n        if (timeDiff > 0) {\n            const speed = distance / timeDiff;\n            maxSpeed = Math.max(maxSpeed, speed);\n        }\n    }\n    return maxSpeed;\n}\n/**\n * Format distance with appropriate unit\n * @param meters Distance in meters\n * @param unit Target unit\n * @returns Formatted distance string\n */ function formatDistance(meters, unit = 'kilometers') {\n    switch(unit){\n        case 'meters':\n            return `${Math.round(meters)} m`;\n        case 'kilometers':\n            return `${(meters / 1000).toFixed(2)} km`;\n        case 'miles':\n            return `${(meters / 1609.34).toFixed(2)} mi`;\n        default:\n            return `${Math.round(meters)} m`;\n    }\n}\n/**\n * Format duration in human-readable format\n * @param seconds Duration in seconds\n * @returns Formatted duration string\n */ function formatDuration(seconds) {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor(seconds % 3600 / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    if (hours > 0) {\n        return `${hours}h ${minutes}m ${remainingSeconds}s`;\n    } else if (minutes > 0) {\n        return `${minutes}m ${remainingSeconds}s`;\n    } else {\n        return `${remainingSeconds}s`;\n    }\n}\n/**\n * Format speed with appropriate unit\n * @param mps Speed in meters per second\n * @param unit Target unit\n * @returns Formatted speed string\n */ function formatSpeed(mps, unit = 'kmh') {\n    switch(unit){\n        case 'mps':\n            return `${mps.toFixed(2)} m/s`;\n        case 'kmh':\n            return `${(mps * 3.6).toFixed(2)} km/h`;\n        case 'mph':\n            return `${(mps * 2.237).toFixed(2)} mph`;\n        default:\n            return `${mps.toFixed(2)} m/s`;\n    }\n}\n/**\n * Check if a coordinate is within a certain radius of a target\n * @param coord Current coordinate\n * @param target Target coordinate\n * @param radius Radius in meters\n * @returns True if within radius\n */ function isWithinRadius(coord, target, radius) {\n    const distance = calculateDistance(coord, target);\n    return distance <= radius;\n}\n/**\n * Generate a unique trail ID\n * @returns Unique trail ID\n */ function generateTrailId() {\n    return `trail_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n}\n/**\n * Validate GPS coordinate\n * @param coord GPS coordinate to validate\n * @returns True if valid\n */ function isValidCoordinate(coord) {\n    return typeof coord.latitude === 'number' && typeof coord.longitude === 'number' && coord.latitude >= -90 && coord.latitude <= 90 && coord.longitude >= -180 && coord.longitude <= 180 && typeof coord.timestamp === 'string' && !isNaN(new Date(coord.timestamp).getTime());\n}\n/**\n * Smooth GPS coordinates by removing outliers\n * @param coordinates Array of GPS coordinates\n * @param maxSpeed Maximum reasonable speed in m/s (default: 20 m/s = 72 km/h)\n * @returns Filtered coordinates\n */ function smoothCoordinates(coordinates, maxSpeed = 20) {\n    if (coordinates.length < 2) return coordinates;\n    const smoothed = [\n        coordinates[0]\n    ];\n    for(let i = 1; i < coordinates.length; i++){\n        const prev = smoothed[smoothed.length - 1];\n        const curr = coordinates[i];\n        const distance = calculateDistance(prev, curr);\n        const timeDiff = (new Date(curr.timestamp).getTime() - new Date(prev.timestamp).getTime()) / 1000;\n        if (timeDiff > 0) {\n            const speed = distance / timeDiff;\n            if (speed <= maxSpeed) {\n                smoothed.push(curr);\n            }\n        // Skip outlier points that indicate unrealistic speed\n        }\n    }\n    return smoothed;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/trailUtils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Chackothon%5CBlockchain%5CVintrek%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Chackothon%5CBlockchain%5CVintrek&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();