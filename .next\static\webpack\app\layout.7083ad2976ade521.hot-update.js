"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"97d52837433c\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxCbG9ja2NoYWluXFxWaW50cmVrXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5N2Q1MjgzNzQzM2NcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers/WalletProvider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/WalletProvider.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletProvider: () => (/* binding */ WalletProvider),\n/* harmony export */   useWallet: () => (/* binding */ useWallet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useWallet,WalletProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst WalletContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useWallet() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(WalletContext);\n    if (context === undefined) {\n        throw new Error('useWallet must be used within a WalletProvider');\n    }\n    return context;\n}\n_s(useWallet, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction WalletProvider(param) {\n    let { children } = param;\n    _s1();\n    const [wallet, setWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connected, setConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [connecting, setConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [address, setAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [balance, setBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getAvailableWallets = ()=>{\n        var _window_cardano, _window_cardano1, _window_cardano2, _window_cardano3, _window_cardano4, _window_cardano5, _window_cardano6, _window_cardano7;\n        if (false) {}\n        const wallets = [];\n        // Check for common Cardano wallets\n        if ((_window_cardano = window.cardano) === null || _window_cardano === void 0 ? void 0 : _window_cardano.lace) wallets.push('lace');\n        if ((_window_cardano1 = window.cardano) === null || _window_cardano1 === void 0 ? void 0 : _window_cardano1.eternl) wallets.push('eternl');\n        if ((_window_cardano2 = window.cardano) === null || _window_cardano2 === void 0 ? void 0 : _window_cardano2.nami) wallets.push('nami');\n        if ((_window_cardano3 = window.cardano) === null || _window_cardano3 === void 0 ? void 0 : _window_cardano3.flint) wallets.push('flint');\n        if ((_window_cardano4 = window.cardano) === null || _window_cardano4 === void 0 ? void 0 : _window_cardano4.typhon) wallets.push('typhon');\n        if ((_window_cardano5 = window.cardano) === null || _window_cardano5 === void 0 ? void 0 : _window_cardano5.gerowallet) wallets.push('gerowallet');\n        if ((_window_cardano6 = window.cardano) === null || _window_cardano6 === void 0 ? void 0 : _window_cardano6.ccvault) wallets.push('ccvault');\n        if ((_window_cardano7 = window.cardano) === null || _window_cardano7 === void 0 ? void 0 : _window_cardano7.yoroi) wallets.push('yoroi');\n        // Debug logging\n        console.log('Checking for wallets...');\n        console.log('window.cardano:', window.cardano);\n        console.log('Available wallets found:', wallets);\n        return wallets;\n    };\n    const connect = async (walletName)=>{\n        if (false) {}\n        setConnecting(true);\n        try {\n            // Check if wallet is available\n            if (!window.cardano || !window.cardano[walletName]) {\n                throw new Error(\"\".concat(walletName, \" wallet not found\"));\n            }\n            // Enable the wallet\n            const walletApi = await window.cardano[walletName].enable();\n            setWallet(walletApi);\n            // Get wallet address\n            try {\n                const addresses = await walletApi.getUsedAddresses();\n                if (addresses.length > 0) {\n                    setAddress(addresses[0]);\n                }\n            } catch (error) {\n                console.warn('Could not fetch addresses:', error);\n            }\n            // Get wallet balance\n            try {\n                const balance = await walletApi.getBalance();\n                setBalance(balance);\n            } catch (error) {\n                console.warn('Could not fetch balance:', error);\n                setBalance('0');\n            }\n            setConnected(true);\n            // Store connection state\n            localStorage.setItem('vintrek_wallet', walletName);\n        } catch (error) {\n            console.error('Failed to connect wallet:', error);\n            throw error;\n        } finally{\n            setConnecting(false);\n        }\n    };\n    const disconnect = ()=>{\n        setWallet(null);\n        setConnected(false);\n        setAddress(null);\n        setBalance(null);\n        localStorage.removeItem('vintrek_wallet');\n    };\n    // Auto-reconnect on page load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletProvider.useEffect\": ()=>{\n            const savedWallet = localStorage.getItem('vintrek_wallet');\n            if (savedWallet && getAvailableWallets().includes(savedWallet)) {\n                connect(savedWallet).catch(console.error);\n            }\n        }\n    }[\"WalletProvider.useEffect\"], []);\n    const value = {\n        wallet,\n        connected,\n        connecting,\n        address,\n        balance,\n        connect,\n        disconnect,\n        getAvailableWallets\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\providers\\\\WalletProvider.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n_s1(WalletProvider, \"dSBI9X6BQq4oMvol2/Gn4R+d8Wg=\");\n_c = WalletProvider;\nvar _c;\n$RefreshReg$(_c, \"WalletProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/WalletProvider.tsx\n"));

/***/ })

});