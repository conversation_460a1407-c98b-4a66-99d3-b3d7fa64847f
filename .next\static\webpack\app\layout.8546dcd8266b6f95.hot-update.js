"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"667498fd314c\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxCbG9ja2NoYWluXFxWaW50cmVrXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2Njc0OThmZDMxNGNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers/WalletProvider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/WalletProvider.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletProvider: () => (/* binding */ WalletProvider),\n/* harmony export */   useWallet: () => (/* binding */ useWallet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useWallet,WalletProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst WalletContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useWallet() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(WalletContext);\n    if (context === undefined) {\n        throw new Error('useWallet must be used within a WalletProvider');\n    }\n    return context;\n}\n_s(useWallet, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction WalletProvider(param) {\n    let { children } = param;\n    _s1();\n    const [wallet, setWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connected, setConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [connecting, setConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [address, setAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [balance, setBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getAvailableWallets = ()=>{\n        var _window_cardano, _window_cardano1, _window_cardano2, _window_cardano3, _window_cardano4, _window_cardano5, _window_cardano6, _window_cardano7;\n        if (false) {}\n        const wallets = [];\n        // Check for common Cardano wallets\n        if ((_window_cardano = window.cardano) === null || _window_cardano === void 0 ? void 0 : _window_cardano.lace) wallets.push('lace');\n        if ((_window_cardano1 = window.cardano) === null || _window_cardano1 === void 0 ? void 0 : _window_cardano1.eternl) wallets.push('eternl');\n        if ((_window_cardano2 = window.cardano) === null || _window_cardano2 === void 0 ? void 0 : _window_cardano2.nami) wallets.push('nami');\n        if ((_window_cardano3 = window.cardano) === null || _window_cardano3 === void 0 ? void 0 : _window_cardano3.flint) wallets.push('flint');\n        if ((_window_cardano4 = window.cardano) === null || _window_cardano4 === void 0 ? void 0 : _window_cardano4.typhon) wallets.push('typhon');\n        if ((_window_cardano5 = window.cardano) === null || _window_cardano5 === void 0 ? void 0 : _window_cardano5.gerowallet) wallets.push('gerowallet');\n        if ((_window_cardano6 = window.cardano) === null || _window_cardano6 === void 0 ? void 0 : _window_cardano6.ccvault) wallets.push('ccvault');\n        if ((_window_cardano7 = window.cardano) === null || _window_cardano7 === void 0 ? void 0 : _window_cardano7.yoroi) wallets.push('yoroi');\n        // Debug logging\n        console.log('Checking for wallets...');\n        console.log('window.cardano:', window.cardano);\n        console.log('Available wallets found:', wallets);\n        return wallets;\n    };\n    const connect = async (walletName)=>{\n        if (false) {}\n        console.log(\"Attempting to connect to \".concat(walletName, \" wallet...\"));\n        setConnecting(true);\n        try {\n            // Check if cardano object exists\n            if (!window.cardano) {\n                throw new Error('No Cardano wallets detected. Please install a Cardano wallet extension.');\n            }\n            // Check if specific wallet is available\n            if (!window.cardano[walletName]) {\n                throw new Error(\"\".concat(walletName, \" wallet not found. Please install \").concat(walletName, \" wallet extension.\"));\n            }\n            console.log(\"Found \".concat(walletName, \" wallet, attempting to enable...\"));\n            // Enable the wallet\n            const walletApi = await window.cardano[walletName].enable();\n            console.log('Wallet enabled successfully:', walletApi);\n            setWallet(walletApi);\n            // Get wallet address\n            try {\n                const addresses = await walletApi.getUsedAddresses();\n                console.log('Fetched addresses:', addresses);\n                if (addresses.length > 0) {\n                    setAddress(addresses[0]);\n                } else {\n                    // Try to get unused addresses if no used addresses\n                    const unusedAddresses = await walletApi.getUnusedAddresses();\n                    if (unusedAddresses.length > 0) {\n                        setAddress(unusedAddresses[0]);\n                    }\n                }\n            } catch (error) {\n                console.warn('Could not fetch addresses:', error);\n            }\n            // Get wallet balance\n            try {\n                const balance = await walletApi.getBalance();\n                console.log('Fetched balance:', balance);\n                setBalance(balance);\n            } catch (error) {\n                console.warn('Could not fetch balance:', error);\n                setBalance('0');\n            }\n            setConnected(true);\n            console.log(\"Successfully connected to \".concat(walletName));\n            // Store connection state\n            localStorage.setItem('vintrek_wallet', walletName);\n        } catch (error) {\n            console.error('Failed to connect wallet:', error);\n            throw error;\n        } finally{\n            setConnecting(false);\n        }\n    };\n    const disconnect = ()=>{\n        setWallet(null);\n        setConnected(false);\n        setAddress(null);\n        setBalance(null);\n        localStorage.removeItem('vintrek_wallet');\n    };\n    // Auto-reconnect on page load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletProvider.useEffect\": ()=>{\n            const savedWallet = localStorage.getItem('vintrek_wallet');\n            if (savedWallet && getAvailableWallets().includes(savedWallet)) {\n                connect(savedWallet).catch(console.error);\n            }\n        }\n    }[\"WalletProvider.useEffect\"], []);\n    const value = {\n        wallet,\n        connected,\n        connecting,\n        address,\n        balance,\n        connect,\n        disconnect,\n        getAvailableWallets\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\providers\\\\WalletProvider.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n_s1(WalletProvider, \"dSBI9X6BQq4oMvol2/Gn4R+d8Wg=\");\n_c = WalletProvider;\nvar _c;\n$RefreshReg$(_c, \"WalletProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/WalletProvider.tsx\n"));

/***/ })

});