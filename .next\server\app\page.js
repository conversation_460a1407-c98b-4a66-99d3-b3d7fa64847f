/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Chackothon%5CBlockchain%5CVintrek%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Chackothon%5CBlockchain%5CVintrek&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Chackothon%5CBlockchain%5CVintrek%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Chackothon%5CBlockchain%5CVintrek&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Chackothon%5CBlockchain%5CVintrek%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Chackothon%5CBlockchain%5CVintrek&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ClientProviders.tsx */ \"(rsc)/./src/components/providers/ClientProviders.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNoYWNrb3Rob24lNUMlNUNCbG9ja2NoYWluJTVDJTVDVmludHJlayU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDaGFja290aG9uJTVDJTVDQmxvY2tjaGFpbiU1QyU1Q1ZpbnRyZWslNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDaGFja290aG9uJTVDJTVDQmxvY2tjaGFpbiU1QyU1Q1ZpbnRyZWslNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDcHJvdmlkZXJzJTVDJTVDQ2xpZW50UHJvdmlkZXJzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkNsaWVudFByb3ZpZGVycyUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd01BQTZKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJDbGllbnRQcm92aWRlcnNcIl0gKi8gXCJEOlxcXFxoYWNrb3Rob25cXFxcQmxvY2tjaGFpblxcXFxWaW50cmVrXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHByb3ZpZGVyc1xcXFxDbGllbnRQcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNoYWNrb3Rob24lNUMlNUNCbG9ja2NoYWluJTVDJTVDVmludHJlayU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBMkYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGhhY2tvdGhvblxcXFxCbG9ja2NoYWluXFxcXFZpbnRyZWtcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"092916a0a569\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxCbG9ja2NoYWluXFxWaW50cmVrXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwOTI5MTZhMGE1NjlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_ClientProviders__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/ClientProviders */ \"(rsc)/./src/components/providers/ClientProviders.tsx\");\n\n\n\n\nconst metadata = {\n    title: 'VinTrek - Blockchain-Powered Eco-Tourism',\n    description: 'Discover, book, and earn rewards for hiking and camping adventures in Sri Lanka. Mint NFTs, earn TREK tokens, and unlock AR/VR experiences.',\n    keywords: 'blockchain, eco-tourism, hiking, camping, NFT, Cardano, Sri Lanka, trails',\n    authors: [\n        {\n            name: 'VinTrek Team'\n        }\n    ],\n    openGraph: {\n        title: 'VinTrek - Blockchain-Powered Eco-Tourism',\n        description: 'Discover trails, mint NFTs, and earn rewards for your outdoor adventures',\n        type: 'website',\n        locale: 'en_US'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_ClientProviders__WEBPACK_IMPORTED_MODULE_2__.ClientProviders, {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\hackothon\\Blockchain\\Vintrek\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/providers/ClientProviders.tsx":
/*!******************************************************!*\
  !*** ./src/components/providers/ClientProviders.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ClientProviders: () => (/* binding */ ClientProviders)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ClientProviders = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ClientProviders() from the server but ClientProviders is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\hackothon\\Blockchain\\Vintrek\\src\\components\\providers\\ClientProviders.tsx",
"ClientProviders",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ClientProviders.tsx */ \"(ssr)/./src/components/providers/ClientProviders.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNoYWNrb3Rob24lNUMlNUNCbG9ja2NoYWluJTVDJTVDVmludHJlayU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDaGFja290aG9uJTVDJTVDQmxvY2tjaGFpbiU1QyU1Q1ZpbnRyZWslNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDaGFja290aG9uJTVDJTVDQmxvY2tjaGFpbiU1QyU1Q1ZpbnRyZWslNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDcHJvdmlkZXJzJTVDJTVDQ2xpZW50UHJvdmlkZXJzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkNsaWVudFByb3ZpZGVycyUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd01BQTZKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJDbGllbnRQcm92aWRlcnNcIl0gKi8gXCJEOlxcXFxoYWNrb3Rob25cXFxcQmxvY2tjaGFpblxcXFxWaW50cmVrXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHByb3ZpZGVyc1xcXFxDbGllbnRQcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNoYWNrb3Rob24lNUMlNUNCbG9ja2NoYWluJTVDJTVDVmludHJlayU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBMkYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGhhY2tvdGhvblxcXFxCbG9ja2NoYWluXFxcXFZpbnRyZWtcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,MapPin,Mountain,Trophy,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mountain.mjs\");\n/* harmony import */ var _barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,MapPin,Mountain,Trophy,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.mjs\");\n/* harmony import */ var _barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,MapPin,Mountain,Trophy,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trophy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,MapPin,Mountain,Trophy,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/coins.mjs\");\n/* harmony import */ var _barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,MapPin,Mountain,Trophy,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.mjs\");\n/* harmony import */ var _components_wallet_WalletConnect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/wallet/WalletConnect */ \"(ssr)/./src/components/wallet/WalletConnect.tsx\");\n/* harmony import */ var _components_trails_TrailCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/trails/TrailCard */ \"(ssr)/./src/components/trails/TrailCard.tsx\");\n/* harmony import */ var _components_ui_StatsCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/StatsCard */ \"(ssr)/./src/components/ui/StatsCard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Mock data for demonstration\nconst featuredTrails = [\n    {\n        id: '1',\n        name: 'Ella Rock Trail',\n        location: 'Ella, Sri Lanka',\n        difficulty: 'Moderate',\n        distance: '8.2 km',\n        duration: '4-5 hours',\n        image: '/api/placeholder/400/300',\n        description: 'A scenic hike offering panoramic views of the hill country.',\n        price: 2500,\n        available: true\n    },\n    {\n        id: '2',\n        name: 'Adam\\'s Peak',\n        location: 'Ratnapura, Sri Lanka',\n        difficulty: 'Hard',\n        distance: '11.5 km',\n        duration: '6-8 hours',\n        image: '/api/placeholder/400/300',\n        description: 'Sacred mountain with breathtaking sunrise views.',\n        price: 3500,\n        available: true\n    },\n    {\n        id: '3',\n        name: 'Horton Plains',\n        location: 'Nuwara Eliya, Sri Lanka',\n        difficulty: 'Easy',\n        distance: '9.5 km',\n        duration: '3-4 hours',\n        image: '/api/placeholder/400/300',\n        description: 'UNESCO World Heritage site with World\\'s End viewpoint.',\n        price: 4000,\n        available: false\n    }\n];\nconst stats = [\n    {\n        icon: _barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        label: 'Trails Available',\n        value: '150+'\n    },\n    {\n        icon: _barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        label: 'Active Hikers',\n        value: '2.5K+'\n    },\n    {\n        icon: _barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        label: 'NFTs Minted',\n        value: '1.2K+'\n    },\n    {\n        icon: _barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        label: 'TREK Tokens Earned',\n        value: '50K+'\n    }\n];\nfunction HomePage() {\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-50 to-blue-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"VinTrek\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"hidden md:flex space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/trails\",\n                                        className: \"text-gray-700 hover:text-green-600 transition-colors\",\n                                        children: \"Trails\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/record\",\n                                        className: \"text-gray-700 hover:text-green-600 transition-colors\",\n                                        children: \"Record\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/dashboard\",\n                                        className: \"text-gray-700 hover:text-green-600 transition-colors\",\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/rewards\",\n                                        className: \"text-gray-700 hover:text-green-600 transition-colors\",\n                                        children: \"Rewards\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-green-600 transition-colors\",\n                                        children: \"Community\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_WalletConnect__WEBPACK_IMPORTED_MODULE_2__.WalletConnect, {\n                                onConnectionChange: setIsConnected\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-20 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl md:text-6xl font-bold text-gray-900 mb-6\",\n                            children: [\n                                \"Explore Sri Lanka's Trails,\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-600 block\",\n                                    children: \"Earn Blockchain Rewards\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\",\n                            children: \"Discover breathtaking hiking trails, mint unique NFTs upon completion, and earn TREK tokens for your eco-tourism adventures.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>window.location.href = '/trails',\n                                    className: \"bg-green-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors\",\n                                    children: \"Explore Trails\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>window.scrollTo({\n                                            top: document.getElementById('how-it-works')?.offsetTop || 0,\n                                            behavior: 'smooth'\n                                        }),\n                                    className: \"border border-green-600 text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-green-50 transition-colors\",\n                                    children: \"Learn More\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 px-4 sm:px-6 lg:px-8 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_StatsCard__WEBPACK_IMPORTED_MODULE_4__.StatsCard, {\n                                ...stat\n                            }, index, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Featured Trails\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 max-w-2xl mx-auto\",\n                                    children: \"Discover our most popular hiking destinations and start your blockchain-powered adventure today.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: featuredTrails.map((trail)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_trails_TrailCard__WEBPACK_IMPORTED_MODULE_3__.TrailCard, {\n                                    trail: trail\n                                }, trail.id, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"how-it-works\",\n                className: \"py-16 px-4 sm:px-6 lg:px-8 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"How VinTrek Works\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 max-w-2xl mx-auto\",\n                                    children: \"Experience the future of eco-tourism with blockchain technology\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-2\",\n                                            children: \"1. Discover & Book\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Browse trails, check availability, and book your adventure with your Cardano wallet.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-8 w-8 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-2\",\n                                            children: \"2. Complete Trail\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Hike the trail, enjoy nature, and verify completion via GPS or manual confirmation.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-8 w-8 text-yellow-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-2\",\n                                            children: \"3. Earn Rewards\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Mint unique trail NFTs and earn TREK tokens for completing adventures and activities.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"VinTrek\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400\",\n                                            children: \"Blockchain-powered eco-tourism platform for Sri Lanka's beautiful trails.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-4\",\n                                            children: \"Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Trails\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Dashboard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Rewards\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Community\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-4\",\n                                            children: \"Blockchain\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"NFT Collection\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"TREK Token\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Wallet Guide\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Smart Contracts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-4\",\n                                            children: \"Support\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Help Center\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Contact Us\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Terms of Service\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Privacy Policy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"\\xa9 2024 VinTrek. All rights reserved. Built on Cardano blockchain.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/ClientProviders.tsx":
/*!******************************************************!*\
  !*** ./src/components/providers/ClientProviders.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientProviders: () => (/* binding */ ClientProviders)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _WalletProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./WalletProvider */ \"(ssr)/./src/components/providers/WalletProvider.tsx\");\n/* harmony import */ var _components_ui_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ErrorBoundary */ \"(ssr)/./src/components/ui/ErrorBoundary.tsx\");\n/* __next_internal_client_entry_do_not_use__ ClientProviders auto */ \n\n\nfunction ClientProviders({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__.ErrorBoundary, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WalletProvider__WEBPACK_IMPORTED_MODULE_1__.WalletProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\providers\\\\ClientProviders.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\providers\\\\ClientProviders.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvQ2xpZW50UHJvdmlkZXJzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFHaUQ7QUFDWTtBQU10RCxTQUFTRSxnQkFBZ0IsRUFBRUMsUUFBUSxFQUF3QjtJQUNoRSxxQkFDRSw4REFBQ0YsdUVBQWFBO2tCQUNaLDRFQUFDRCwyREFBY0E7c0JBQ1pHOzs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxCbG9ja2NoYWluXFxWaW50cmVrXFxzcmNcXGNvbXBvbmVudHNcXHByb3ZpZGVyc1xcQ2xpZW50UHJvdmlkZXJzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBXYWxsZXRQcm92aWRlciB9IGZyb20gJy4vV2FsbGV0UHJvdmlkZXInXG5pbXBvcnQgeyBFcnJvckJvdW5kYXJ5IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL0Vycm9yQm91bmRhcnknXG5cbmludGVyZmFjZSBDbGllbnRQcm92aWRlcnNQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIENsaWVudFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IENsaWVudFByb3ZpZGVyc1Byb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPEVycm9yQm91bmRhcnk+XG4gICAgICA8V2FsbGV0UHJvdmlkZXI+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvV2FsbGV0UHJvdmlkZXI+XG4gICAgPC9FcnJvckJvdW5kYXJ5PlxuICApXG59XG4iXSwibmFtZXMiOlsiV2FsbGV0UHJvdmlkZXIiLCJFcnJvckJvdW5kYXJ5IiwiQ2xpZW50UHJvdmlkZXJzIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/ClientProviders.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/WalletProvider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/WalletProvider.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletProvider: () => (/* binding */ WalletProvider),\n/* harmony export */   useWallet: () => (/* binding */ useWallet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useWallet,WalletProvider auto */ \n\nconst WalletContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useWallet() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(WalletContext);\n    if (context === undefined) {\n        throw new Error('useWallet must be used within a WalletProvider');\n    }\n    return context;\n}\nfunction WalletProvider({ children }) {\n    const [wallet, setWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connected, setConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [connecting, setConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [address, setAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [balance, setBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getAvailableWallets = ()=>{\n        if (true) return [];\n        const wallets = [];\n        if (window.cardano?.lace) wallets.push('lace');\n        if (window.cardano?.eternl) wallets.push('eternl');\n        if (window.cardano?.nami) wallets.push('nami');\n        if (window.cardano?.flint) wallets.push('flint');\n        return wallets;\n    };\n    const connect = async (walletName)=>{\n        if (true) return;\n        setConnecting(true);\n        try {\n            // Check if wallet is available\n            if (!window.cardano || !window.cardano[walletName]) {\n                throw new Error(`${walletName} wallet not found`);\n            }\n            // Enable the wallet\n            const walletApi = await window.cardano[walletName].enable();\n            setWallet(walletApi);\n            // Get wallet address\n            try {\n                const addresses = await walletApi.getUsedAddresses();\n                if (addresses.length > 0) {\n                    setAddress(addresses[0]);\n                }\n            } catch (error) {\n                console.warn('Could not fetch addresses:', error);\n            }\n            // Get wallet balance\n            try {\n                const balance = await walletApi.getBalance();\n                setBalance(balance);\n            } catch (error) {\n                console.warn('Could not fetch balance:', error);\n                setBalance('0');\n            }\n            setConnected(true);\n            // Store connection state\n            localStorage.setItem('vintrek_wallet', walletName);\n        } catch (error) {\n            console.error('Failed to connect wallet:', error);\n            throw error;\n        } finally{\n            setConnecting(false);\n        }\n    };\n    const disconnect = ()=>{\n        setWallet(null);\n        setConnected(false);\n        setAddress(null);\n        setBalance(null);\n        localStorage.removeItem('vintrek_wallet');\n    };\n    // Auto-reconnect on page load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletProvider.useEffect\": ()=>{\n            const savedWallet = localStorage.getItem('vintrek_wallet');\n            if (savedWallet && getAvailableWallets().includes(savedWallet)) {\n                connect(savedWallet).catch(console.error);\n            }\n        }\n    }[\"WalletProvider.useEffect\"], []);\n    const value = {\n        wallet,\n        connected,\n        connecting,\n        address,\n        balance,\n        connect,\n        disconnect,\n        getAvailableWallets\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\providers\\\\WalletProvider.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvV2FsbGV0UHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFaUY7QUFhakYsTUFBTUksOEJBQWdCSixvREFBYUEsQ0FBZ0NLO0FBRTVELFNBQVNDO0lBQ2QsTUFBTUMsVUFBVU4saURBQVVBLENBQUNHO0lBQzNCLElBQUlHLFlBQVlGLFdBQVc7UUFDekIsTUFBTSxJQUFJRyxNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVDtBQU1PLFNBQVNFLGVBQWUsRUFBRUMsUUFBUSxFQUF1QjtJQUM5RCxNQUFNLENBQUNDLFFBQVFDLFVBQVUsR0FBR1YsK0NBQVFBLENBQWE7SUFDakQsTUFBTSxDQUFDVyxXQUFXQyxhQUFhLEdBQUdaLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ2EsWUFBWUMsY0FBYyxHQUFHZCwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNlLFNBQVNDLFdBQVcsR0FBR2hCLCtDQUFRQSxDQUFnQjtJQUN0RCxNQUFNLENBQUNpQixTQUFTQyxXQUFXLEdBQUdsQiwrQ0FBUUEsQ0FBZ0I7SUFFdEQsTUFBTW1CLHNCQUFzQjtRQUMxQixJQUFJLElBQTZCLEVBQUUsT0FBTyxFQUFFO1FBRTVDLE1BQU1DLFVBQVUsRUFBRTtRQUNsQixJQUFJQyxPQUFPQyxPQUFPLEVBQUVDLE1BQU1ILFFBQVFJLElBQUksQ0FBQztRQUN2QyxJQUFJSCxPQUFPQyxPQUFPLEVBQUVHLFFBQVFMLFFBQVFJLElBQUksQ0FBQztRQUN6QyxJQUFJSCxPQUFPQyxPQUFPLEVBQUVJLE1BQU1OLFFBQVFJLElBQUksQ0FBQztRQUN2QyxJQUFJSCxPQUFPQyxPQUFPLEVBQUVLLE9BQU9QLFFBQVFJLElBQUksQ0FBQztRQUV4QyxPQUFPSjtJQUNUO0lBRUEsTUFBTVEsVUFBVSxPQUFPQztRQUNyQixJQUFJLElBQTZCLEVBQUU7UUFFbkNmLGNBQWM7UUFDZCxJQUFJO1lBQ0YsK0JBQStCO1lBQy9CLElBQUksQ0FBQ08sT0FBT0MsT0FBTyxJQUFJLENBQUNELE9BQU9DLE9BQU8sQ0FBQ08sV0FBVyxFQUFFO2dCQUNsRCxNQUFNLElBQUl2QixNQUFNLEdBQUd1QixXQUFXLGlCQUFpQixDQUFDO1lBQ2xEO1lBRUEsb0JBQW9CO1lBQ3BCLE1BQU1DLFlBQVksTUFBTVQsT0FBT0MsT0FBTyxDQUFDTyxXQUFXLENBQUNFLE1BQU07WUFDekRyQixVQUFVb0I7WUFFVixxQkFBcUI7WUFDckIsSUFBSTtnQkFDRixNQUFNRSxZQUFZLE1BQU1GLFVBQVVHLGdCQUFnQjtnQkFDbEQsSUFBSUQsVUFBVUUsTUFBTSxHQUFHLEdBQUc7b0JBQ3hCbEIsV0FBV2dCLFNBQVMsQ0FBQyxFQUFFO2dCQUN6QjtZQUNGLEVBQUUsT0FBT0csT0FBTztnQkFDZEMsUUFBUUMsSUFBSSxDQUFDLDhCQUE4QkY7WUFDN0M7WUFFQSxxQkFBcUI7WUFDckIsSUFBSTtnQkFDRixNQUFNbEIsVUFBVSxNQUFNYSxVQUFVUSxVQUFVO2dCQUMxQ3BCLFdBQVdEO1lBQ2IsRUFBRSxPQUFPa0IsT0FBTztnQkFDZEMsUUFBUUMsSUFBSSxDQUFDLDRCQUE0QkY7Z0JBQ3pDakIsV0FBVztZQUNiO1lBRUFOLGFBQWE7WUFFYix5QkFBeUI7WUFDekIyQixhQUFhQyxPQUFPLENBQUMsa0JBQWtCWDtRQUN6QyxFQUFFLE9BQU9NLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkE7WUFDM0MsTUFBTUE7UUFDUixTQUFVO1lBQ1JyQixjQUFjO1FBQ2hCO0lBQ0Y7SUFFQSxNQUFNMkIsYUFBYTtRQUNqQi9CLFVBQVU7UUFDVkUsYUFBYTtRQUNiSSxXQUFXO1FBQ1hFLFdBQVc7UUFDWHFCLGFBQWFHLFVBQVUsQ0FBQztJQUMxQjtJQUVBLDhCQUE4QjtJQUM5QnpDLGdEQUFTQTtvQ0FBQztZQUNSLE1BQU0wQyxjQUFjSixhQUFhSyxPQUFPLENBQUM7WUFDekMsSUFBSUQsZUFBZXhCLHNCQUFzQjBCLFFBQVEsQ0FBQ0YsY0FBYztnQkFDOURmLFFBQVFlLGFBQWFHLEtBQUssQ0FBQ1YsUUFBUUQsS0FBSztZQUMxQztRQUNGO21DQUFHLEVBQUU7SUFFTCxNQUFNWSxRQUEyQjtRQUMvQnRDO1FBQ0FFO1FBQ0FFO1FBQ0FFO1FBQ0FFO1FBQ0FXO1FBQ0FhO1FBQ0F0QjtJQUNGO0lBRUEscUJBQ0UsOERBQUNqQixjQUFjOEMsUUFBUTtRQUFDRCxPQUFPQTtrQkFDNUJ2Qzs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIkQ6XFxoYWNrb3Rob25cXEJsb2NrY2hhaW5cXFZpbnRyZWtcXHNyY1xcY29tcG9uZW50c1xccHJvdmlkZXJzXFxXYWxsZXRQcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZVN0YXRlLCB1c2VFZmZlY3QsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0J1xuXG5pbnRlcmZhY2UgV2FsbGV0Q29udGV4dFR5cGUge1xuICB3YWxsZXQ6IGFueSB8IG51bGxcbiAgY29ubmVjdGVkOiBib29sZWFuXG4gIGNvbm5lY3Rpbmc6IGJvb2xlYW5cbiAgYWRkcmVzczogc3RyaW5nIHwgbnVsbFxuICBiYWxhbmNlOiBzdHJpbmcgfCBudWxsXG4gIGNvbm5lY3Q6ICh3YWxsZXROYW1lOiBzdHJpbmcpID0+IFByb21pc2U8dm9pZD5cbiAgZGlzY29ubmVjdDogKCkgPT4gdm9pZFxuICBnZXRBdmFpbGFibGVXYWxsZXRzOiAoKSA9PiBzdHJpbmdbXVxufVxuXG5jb25zdCBXYWxsZXRDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxXYWxsZXRDb250ZXh0VHlwZSB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlV2FsbGV0KCkge1xuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChXYWxsZXRDb250ZXh0KVxuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VXYWxsZXQgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIFdhbGxldFByb3ZpZGVyJylcbiAgfVxuICByZXR1cm4gY29udGV4dFxufVxuXG5pbnRlcmZhY2UgV2FsbGV0UHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFdhbGxldFByb3ZpZGVyKHsgY2hpbGRyZW4gfTogV2FsbGV0UHJvdmlkZXJQcm9wcykge1xuICBjb25zdCBbd2FsbGV0LCBzZXRXYWxsZXRdID0gdXNlU3RhdGU8YW55IHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2Nvbm5lY3RlZCwgc2V0Q29ubmVjdGVkXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbY29ubmVjdGluZywgc2V0Q29ubmVjdGluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2FkZHJlc3MsIHNldEFkZHJlc3NdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2JhbGFuY2UsIHNldEJhbGFuY2VdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcblxuICBjb25zdCBnZXRBdmFpbGFibGVXYWxsZXRzID0gKCkgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuIFtdXG4gICAgXG4gICAgY29uc3Qgd2FsbGV0cyA9IFtdXG4gICAgaWYgKHdpbmRvdy5jYXJkYW5vPy5sYWNlKSB3YWxsZXRzLnB1c2goJ2xhY2UnKVxuICAgIGlmICh3aW5kb3cuY2FyZGFubz8uZXRlcm5sKSB3YWxsZXRzLnB1c2goJ2V0ZXJubCcpXG4gICAgaWYgKHdpbmRvdy5jYXJkYW5vPy5uYW1pKSB3YWxsZXRzLnB1c2goJ25hbWknKVxuICAgIGlmICh3aW5kb3cuY2FyZGFubz8uZmxpbnQpIHdhbGxldHMucHVzaCgnZmxpbnQnKVxuICAgIFxuICAgIHJldHVybiB3YWxsZXRzXG4gIH1cblxuICBjb25zdCBjb25uZWN0ID0gYXN5bmMgKHdhbGxldE5hbWU6IHN0cmluZykgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuXG5cbiAgICBzZXRDb25uZWN0aW5nKHRydWUpXG4gICAgdHJ5IHtcbiAgICAgIC8vIENoZWNrIGlmIHdhbGxldCBpcyBhdmFpbGFibGVcbiAgICAgIGlmICghd2luZG93LmNhcmRhbm8gfHwgIXdpbmRvdy5jYXJkYW5vW3dhbGxldE5hbWVdKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgJHt3YWxsZXROYW1lfSB3YWxsZXQgbm90IGZvdW5kYClcbiAgICAgIH1cblxuICAgICAgLy8gRW5hYmxlIHRoZSB3YWxsZXRcbiAgICAgIGNvbnN0IHdhbGxldEFwaSA9IGF3YWl0IHdpbmRvdy5jYXJkYW5vW3dhbGxldE5hbWVdLmVuYWJsZSgpXG4gICAgICBzZXRXYWxsZXQod2FsbGV0QXBpKVxuXG4gICAgICAvLyBHZXQgd2FsbGV0IGFkZHJlc3NcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGFkZHJlc3NlcyA9IGF3YWl0IHdhbGxldEFwaS5nZXRVc2VkQWRkcmVzc2VzKClcbiAgICAgICAgaWYgKGFkZHJlc3Nlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgc2V0QWRkcmVzcyhhZGRyZXNzZXNbMF0pXG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUud2FybignQ291bGQgbm90IGZldGNoIGFkZHJlc3NlczonLCBlcnJvcilcbiAgICAgIH1cblxuICAgICAgLy8gR2V0IHdhbGxldCBiYWxhbmNlXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBiYWxhbmNlID0gYXdhaXQgd2FsbGV0QXBpLmdldEJhbGFuY2UoKVxuICAgICAgICBzZXRCYWxhbmNlKGJhbGFuY2UpXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLndhcm4oJ0NvdWxkIG5vdCBmZXRjaCBiYWxhbmNlOicsIGVycm9yKVxuICAgICAgICBzZXRCYWxhbmNlKCcwJylcbiAgICAgIH1cblxuICAgICAgc2V0Q29ubmVjdGVkKHRydWUpXG5cbiAgICAgIC8vIFN0b3JlIGNvbm5lY3Rpb24gc3RhdGVcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCd2aW50cmVrX3dhbGxldCcsIHdhbGxldE5hbWUpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBjb25uZWN0IHdhbGxldDonLCBlcnJvcilcbiAgICAgIHRocm93IGVycm9yXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldENvbm5lY3RpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZGlzY29ubmVjdCA9ICgpID0+IHtcbiAgICBzZXRXYWxsZXQobnVsbClcbiAgICBzZXRDb25uZWN0ZWQoZmFsc2UpXG4gICAgc2V0QWRkcmVzcyhudWxsKVxuICAgIHNldEJhbGFuY2UobnVsbClcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgndmludHJla193YWxsZXQnKVxuICB9XG5cbiAgLy8gQXV0by1yZWNvbm5lY3Qgb24gcGFnZSBsb2FkXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3Qgc2F2ZWRXYWxsZXQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndmludHJla193YWxsZXQnKVxuICAgIGlmIChzYXZlZFdhbGxldCAmJiBnZXRBdmFpbGFibGVXYWxsZXRzKCkuaW5jbHVkZXMoc2F2ZWRXYWxsZXQpKSB7XG4gICAgICBjb25uZWN0KHNhdmVkV2FsbGV0KS5jYXRjaChjb25zb2xlLmVycm9yKVxuICAgIH1cbiAgfSwgW10pXG5cbiAgY29uc3QgdmFsdWU6IFdhbGxldENvbnRleHRUeXBlID0ge1xuICAgIHdhbGxldCxcbiAgICBjb25uZWN0ZWQsXG4gICAgY29ubmVjdGluZyxcbiAgICBhZGRyZXNzLFxuICAgIGJhbGFuY2UsXG4gICAgY29ubmVjdCxcbiAgICBkaXNjb25uZWN0LFxuICAgIGdldEF2YWlsYWJsZVdhbGxldHMsXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxXYWxsZXRDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9XYWxsZXRDb250ZXh0LlByb3ZpZGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIldhbGxldENvbnRleHQiLCJ1bmRlZmluZWQiLCJ1c2VXYWxsZXQiLCJjb250ZXh0IiwiRXJyb3IiLCJXYWxsZXRQcm92aWRlciIsImNoaWxkcmVuIiwid2FsbGV0Iiwic2V0V2FsbGV0IiwiY29ubmVjdGVkIiwic2V0Q29ubmVjdGVkIiwiY29ubmVjdGluZyIsInNldENvbm5lY3RpbmciLCJhZGRyZXNzIiwic2V0QWRkcmVzcyIsImJhbGFuY2UiLCJzZXRCYWxhbmNlIiwiZ2V0QXZhaWxhYmxlV2FsbGV0cyIsIndhbGxldHMiLCJ3aW5kb3ciLCJjYXJkYW5vIiwibGFjZSIsInB1c2giLCJldGVybmwiLCJuYW1pIiwiZmxpbnQiLCJjb25uZWN0Iiwid2FsbGV0TmFtZSIsIndhbGxldEFwaSIsImVuYWJsZSIsImFkZHJlc3NlcyIsImdldFVzZWRBZGRyZXNzZXMiLCJsZW5ndGgiLCJlcnJvciIsImNvbnNvbGUiLCJ3YXJuIiwiZ2V0QmFsYW5jZSIsImxvY2FsU3RvcmFnZSIsInNldEl0ZW0iLCJkaXNjb25uZWN0IiwicmVtb3ZlSXRlbSIsInNhdmVkV2FsbGV0IiwiZ2V0SXRlbSIsImluY2x1ZGVzIiwiY2F0Y2giLCJ2YWx1ZSIsIlByb3ZpZGVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/WalletProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/trails/BookingModal.tsx":
/*!************************************************!*\
  !*** ./src/components/trails/BookingModal.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BookingModal: () => (/* binding */ BookingModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,MapPin,TrendingUp,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,MapPin,TrendingUp,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,MapPin,TrendingUp,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,MapPin,TrendingUp,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,MapPin,TrendingUp,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,MapPin,TrendingUp,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,MapPin,TrendingUp,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,MapPin,TrendingUp,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wallet.mjs\");\n/* harmony import */ var _components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/WalletProvider */ \"(ssr)/./src/components/providers/WalletProvider.tsx\");\n/* harmony import */ var _lib_blockchain__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/blockchain */ \"(ssr)/./src/lib/blockchain.ts\");\n/* __next_internal_client_entry_do_not_use__ BookingModal auto */ \n\n\n\n\nfunction BookingModal({ trail, isOpen, onClose }) {\n    const { connected, wallet } = (0,_components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__.useWallet)();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [participants, setParticipants] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isBooking, setIsBooking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [bookingError, setBookingError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    if (!isOpen) return null;\n    const totalPrice = trail.price * participants;\n    const availableSpots = trail.maxCapacity - trail.currentBookings;\n    const handleBooking = async ()=>{\n        if (!connected || !wallet) {\n            setBookingError('Please connect your wallet to book this trail.');\n            return;\n        }\n        if (!selectedDate) {\n            setBookingError('Please select a date for your adventure.');\n            return;\n        }\n        if (participants > availableSpots) {\n            setBookingError(`Only ${availableSpots} spots available.`);\n            return;\n        }\n        setIsBooking(true);\n        setBookingError('');\n        try {\n            _lib_blockchain__WEBPACK_IMPORTED_MODULE_3__.blockchainService.setWallet(wallet);\n            // Create booking transaction\n            const txHash = await _lib_blockchain__WEBPACK_IMPORTED_MODULE_3__.blockchainService.createBookingTransaction(trail.id, totalPrice / 1000, selectedDate);\n            alert(`Booking successful! Transaction hash: ${txHash}`);\n            onClose();\n        } catch (error) {\n            console.error('Booking failed:', error);\n            setBookingError('Booking failed. Please try again or check your wallet balance.');\n        } finally{\n            setIsBooking(false);\n        }\n    };\n    const getTomorrowDate = ()=>{\n        const tomorrow = new Date();\n        tomorrow.setDate(tomorrow.getDate() + 1);\n        return tomorrow.toISOString().split('T')[0];\n    };\n    const getMaxDate = ()=>{\n        const maxDate = new Date();\n        maxDate.setDate(maxDate.getDate() + 90) // 3 months ahead\n        ;\n        return maxDate.toISOString().split('T')[0];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Book Your Adventure\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                            children: trail.name\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 text-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: trail.location\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 text-green-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: trail.distance\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 text-purple-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: trail.duration\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `px-2 py-1 rounded-full text-xs ${trail.difficulty === 'Easy' ? 'bg-green-100 text-green-800' : trail.difficulty === 'Moderate' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`,\n                                        children: trail.difficulty\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-3\",\n                            children: trail.description\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Select Date\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"date\",\n                                                    value: selectedDate,\n                                                    onChange: (e)=>setSelectedDate(e.target.value),\n                                                    min: getTomorrowDate(),\n                                                    max: getMaxDate(),\n                                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Participants\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: participants,\n                                                    onChange: (e)=>setParticipants(parseInt(e.target.value)),\n                                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent\",\n                                                    children: Array.from({\n                                                        length: Math.min(availableSpots, 10)\n                                                    }, (_, i)=>i + 1).map((num)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: num,\n                                                            children: [\n                                                                num,\n                                                                \" \",\n                                                                num === 1 ? 'person' : 'people'\n                                                            ]\n                                                        }, num, true, {\n                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: [\n                                                availableSpots,\n                                                \" spots available\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 p-4 bg-gray-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Price per person:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: [\n                                                \"₨\",\n                                                trail.price.toLocaleString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Participants:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: participants\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t pt-2 flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"Total:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-green-600\",\n                                            children: [\n                                                \"₨\",\n                                                totalPrice.toLocaleString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-4 bg-green-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-green-800 mb-2\",\n                                    children: \"Blockchain Rewards\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-green-700 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"✓ Earn 50 TREK tokens per person upon completion\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"✓ Mint unique trail completion NFT\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"✓ On-chain booking confirmation\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this),\n                        bookingError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-4 bg-red-50 rounded-lg flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5 text-red-500 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-700\",\n                                    children: bookingError\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 13\n                        }, this),\n                        !connected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-4 bg-yellow-50 rounded-lg flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5 text-yellow-500 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-yellow-700\",\n                                    children: \"Connect your Cardano wallet to proceed with booking\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 flex space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"flex-1 py-3 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleBooking,\n                                    disabled: !connected || isBooking || !selectedDate || !trail.available,\n                                    className: `flex-1 py-3 px-4 rounded-lg font-medium transition-colors ${connected && !isBooking && selectedDate && trail.available ? 'bg-green-600 text-white hover:bg-green-700' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`,\n                                    children: isBooking ? 'Processing...' : `Book for ₨${totalPrice.toLocaleString()}`\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-xs text-gray-500 text-center\",\n                            children: \"By booking, you agree to our terms and conditions. Cancellations must be made 24 hours in advance.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/trails/BookingModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/trails/TrailCard.tsx":
/*!*********************************************!*\
  !*** ./src/components/trails/TrailCard.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrailCard: () => (/* binding */ TrailCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_MapPin_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,MapPin,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_MapPin_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,MapPin,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_MapPin_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,MapPin,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_MapPin_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,MapPin,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.mjs\");\n/* harmony import */ var _BookingModal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BookingModal */ \"(ssr)/./src/components/trails/BookingModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ TrailCard auto */ \n\n\n\nfunction TrailCard({ trail }) {\n    const [showBookingModal, setShowBookingModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getDifficultyColor = (difficulty)=>{\n        switch(difficulty.toLowerCase()){\n            case 'easy':\n                return 'bg-green-100 text-green-800';\n            case 'moderate':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'hard':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const handleBookTrail = ()=>{\n        if (!trail.available) return;\n        setShowBookingModal(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                onClick: ()=>window.location.href = `/trails/${trail.id}`,\n                className: \"cursor-pointer\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-48 bg-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-4 left-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(trail.difficulty)}`,\n                                children: trail.difficulty\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-4 right-4\",\n                            children: trail.available ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium\",\n                                children: \"Available\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium\",\n                                children: \"Booked\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full flex items-center justify-center text-gray-400\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_MapPin_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-12 w-12\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                children: trail.name\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-gray-600 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_MapPin_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: trail.location\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 text-sm\",\n                                children: trail.description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_MapPin_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: trail.distance\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_MapPin_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: trail.duration\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t pt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: [\n                                                    \"₨\",\n                                                    trail.price.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"per person\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-green-600\",\n                                                children: \"Earn Rewards\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"50 TREK + NFT\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleBookTrail,\n                                        disabled: !trail.available,\n                                        className: `flex-1 py-2 px-4 rounded-lg font-medium transition-colors ${trail.available ? 'bg-green-600 text-white hover:bg-green-700' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`,\n                                        children: trail.available ? 'Book Trail' : 'Unavailable'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        title: \"View availability calendar\",\n                                        className: \"px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors\",\n                                        onClick: ()=>setShowBookingModal(true),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_MapPin_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs font-medium text-blue-800 mb-1\",\n                                children: \"Blockchain Features\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-xs text-blue-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ On-chain booking\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ NFT certificate\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ Token rewards\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BookingModal__WEBPACK_IMPORTED_MODULE_2__.BookingModal, {\n                trail: trail,\n                isOpen: showBookingModal,\n                onClose: ()=>setShowBookingModal(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/trails/TrailCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ErrorBoundary.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/ErrorBoundary.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),\n/* harmony export */   useErrorHandler: () => (/* binding */ useErrorHandler)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.mjs\");\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary,useErrorHandler auto */ \n\n\nclass ErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props), this.resetError = ()=>{\n            this.setState({\n                hasError: false,\n                error: undefined\n            });\n        };\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error('ErrorBoundary caught an error:', error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                const FallbackComponent = this.props.fallback;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FallbackComponent, {\n                    error: this.state.error,\n                    resetError: this.resetError\n                }, void 0, false, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 16\n                }, this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DefaultErrorFallback, {\n                error: this.state.error,\n                resetError: this.resetError\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                lineNumber: 41,\n                columnNumber: 14\n            }, this);\n        }\n        return this.props.children;\n    }\n}\nfunction DefaultErrorFallback({ error, resetError }) {\n    const isBlockchainError = error?.message?.includes('blockchain') || error?.message?.includes('wallet') || error?.message?.includes('WASM');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"h-16 w-16 text-red-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: isBlockchainError ? 'Blockchain Connection Error' : 'Something went wrong'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: isBlockchainError ? 'There was an issue connecting to the blockchain services. This might be due to network issues or browser compatibility.' : 'An unexpected error occurred. Please try refreshing the page.'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                        className: \"mb-6 text-left\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                className: \"cursor-pointer text-sm text-gray-500 hover:text-gray-700\",\n                                children: \"Technical details\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-32\",\n                                children: error.message\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: resetError,\n                                className: \"w-full flex items-center justify-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Try Again\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.location.href = '/',\n                                className: \"w-full flex items-center justify-center space-x-2 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Go Home\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this),\n                    isBlockchainError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 bg-blue-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-blue-800 mb-2\",\n                                children: \"Troubleshooting Tips:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-xs text-blue-700 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Make sure you're using a modern browser (Chrome, Firefox, Edge)\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Check your internet connection\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Try refreshing the page\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Clear your browser cache if the issue persists\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n// Hook for functional components\nfunction useErrorHandler() {\n    return (error)=>{\n        console.error('Error caught by useErrorHandler:', error);\n    // You could also send this to an error reporting service\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/StatsCard.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/StatsCard.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatsCard: () => (/* binding */ StatsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ StatsCard auto */ \nfunction StatsCard({ icon: Icon, label, value }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"h-6 w-6 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\StatsCard.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\StatsCard.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-2xl font-bold text-gray-900 mb-1\",\n                children: value\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\StatsCard.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-600 text-sm\",\n                children: label\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\StatsCard.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\StatsCard.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9TdGF0c0NhcmQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFVTyxTQUFTQSxVQUFVLEVBQUVDLE1BQU1DLElBQUksRUFBRUMsS0FBSyxFQUFFQyxLQUFLLEVBQWtCO0lBQ3BFLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNKO29CQUFLSSxXQUFVOzs7Ozs7Ozs7OzswQkFFbEIsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUF5Q0Y7Ozs7OzswQkFDeEQsOERBQUNDO2dCQUFJQyxXQUFVOzBCQUF5Qkg7Ozs7Ozs7Ozs7OztBQUc5QyIsInNvdXJjZXMiOlsiRDpcXGhhY2tvdGhvblxcQmxvY2tjaGFpblxcVmludHJla1xcc3JjXFxjb21wb25lbnRzXFx1aVxcU3RhdHNDYXJkLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgTHVjaWRlSWNvbiB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcblxuaW50ZXJmYWNlIFN0YXRzQ2FyZFByb3BzIHtcbiAgaWNvbjogTHVjaWRlSWNvblxuICBsYWJlbDogc3RyaW5nXG4gIHZhbHVlOiBzdHJpbmdcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFN0YXRzQ2FyZCh7IGljb246IEljb24sIGxhYmVsLCB2YWx1ZSB9OiBTdGF0c0NhcmRQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JlZW4tMTAwIHctMTIgaC0xMiByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi0zXCI+XG4gICAgICAgIDxJY29uIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1ncmVlbi02MDBcIiAvPlxuICAgICAgPC9kaXY+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTFcIj57dmFsdWV9PC9kaXY+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgdGV4dC1zbVwiPntsYWJlbH08L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlN0YXRzQ2FyZCIsImljb24iLCJJY29uIiwibGFiZWwiLCJ2YWx1ZSIsImRpdiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/StatsCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/wallet/WalletConnect.tsx":
/*!*************************************************!*\
  !*** ./src/components/wallet/WalletConnect.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletConnect: () => (/* binding */ WalletConnect)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Copy,ExternalLink,LogOut,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wallet.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Copy,ExternalLink,LogOut,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Copy,ExternalLink,LogOut,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Copy,ExternalLink,LogOut,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Copy,ExternalLink,LogOut,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.mjs\");\n/* harmony import */ var _components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/WalletProvider */ \"(ssr)/./src/components/providers/WalletProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ WalletConnect auto */ \n\n\n\nfunction WalletConnect({ onConnectionChange }) {\n    const { connected, connecting, address, balance, connect, disconnect, getAvailableWallets } = (0,_components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__.useWallet)();\n    const [showWalletModal, setShowWalletModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAccountModal, setShowAccountModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const availableWallets = getAvailableWallets();\n    const handleConnect = async (walletName)=>{\n        try {\n            await connect(walletName);\n            setShowWalletModal(false);\n            onConnectionChange?.(true);\n        } catch (error) {\n            console.error('Connection failed:', error);\n            alert('Failed to connect wallet. Please try again.');\n        }\n    };\n    const handleDisconnect = ()=>{\n        disconnect();\n        setShowAccountModal(false);\n        onConnectionChange?.(false);\n    };\n    const copyAddress = ()=>{\n        if (address) {\n            navigator.clipboard.writeText(address);\n            alert('Address copied to clipboard!');\n        }\n    };\n    const formatAddress = (addr)=>{\n        return `${addr.slice(0, 8)}...${addr.slice(-8)}`;\n    };\n    const formatBalance = (bal)=>{\n        try {\n            const lovelace = parseInt(bal);\n            const ada = lovelace / 1000000;\n            return `${ada.toFixed(2)} ADA`;\n        } catch  {\n            return '0.00 ADA';\n        }\n    };\n    if (connected && address) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setShowAccountModal(!showAccountModal),\n                    className: \"flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"hidden sm:inline\",\n                            children: formatAddress(address)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                showAccountModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border z-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Connected Wallet\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowAccountModal(false),\n                                            className: \"text-gray-400 hover:text-gray-600\",\n                                            children: \"\\xd7\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: balance ? formatBalance(balance) : 'Loading...'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Address\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-mono text-sm\",\n                                                children: formatAddress(address)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: copyAddress,\n                                                className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                title: \"Copy address\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.open(`https://cardanoscan.io/address/${address}`, '_blank'),\n                                                className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                title: \"View on explorer\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleDisconnect,\n                                className: \"w-full flex items-center justify-center space-x-2 text-red-600 hover:bg-red-50 py-2 px-4 rounded-lg transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Disconnect\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setShowWalletModal(true),\n                disabled: connecting,\n                className: \"flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: connecting ? 'Connecting...' : 'Connect Wallet'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            showWalletModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"Connect Wallet\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowWalletModal(false),\n                                    className: \"text-gray-400 hover:text-gray-600\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this),\n                        availableWallets.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-12 w-12 text-gray-300 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"No Wallets Found\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Please install a Cardano wallet to continue.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://www.lace.io/\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"block w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors\",\n                                            children: \"Install Lace Wallet\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://eternl.io/\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"block w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors\",\n                                            children: \"Install Eternl Wallet\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>window.location.reload(),\n                                            className: \"block w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors\",\n                                            children: \"Refresh Page (After Installing Wallet)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Choose a wallet to connect to VinTrek:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 17\n                                }, this),\n                                availableWallets.map((walletName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleConnect(walletName),\n                                        disabled: connecting,\n                                        className: \"w-full flex items-center space-x-3 p-4 border rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium capitalize\",\n                                                        children: walletName\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            walletName === 'lace' && 'Lace Wallet',\n                                                            walletName === 'eternl' && 'Eternl Wallet',\n                                                            walletName === 'nami' && 'Nami Wallet',\n                                                            walletName === 'flint' && 'Flint Wallet'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, walletName, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 19\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/wallet/WalletConnect.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/blockchain.ts":
/*!*******************************!*\
  !*** ./src/lib/blockchain.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BLOCKCHAIN_CONFIG: () => (/* binding */ BLOCKCHAIN_CONFIG),\n/* harmony export */   BlockchainService: () => (/* binding */ BlockchainService),\n/* harmony export */   TREK_TOKEN: () => (/* binding */ TREK_TOKEN),\n/* harmony export */   blockchainService: () => (/* binding */ blockchainService),\n/* harmony export */   formatAdaToLovelace: () => (/* binding */ formatAdaToLovelace),\n/* harmony export */   formatLovelaceToAda: () => (/* binding */ formatLovelaceToAda),\n/* harmony export */   generateAssetName: () => (/* binding */ generateAssetName)\n/* harmony export */ });\n// Dynamic imports for client-side only\nlet BrowserWallet = null;\nlet Transaction = null;\nlet AssetMetadata = null;\nlet meshLoaded = false;\nconst loadMeshSDK = async ()=>{\n    if (false) {}\n};\n// Initialize on client side\nif (false) {}\n// Blockchain configuration\nconst BLOCKCHAIN_CONFIG = {\n    network: \"testnet\" || 0,\n    blockfrostApiKey: \"testnetYourProjectIdHere\" || 0,\n    blockfrostUrl: \"https://cardano-testnet.blockfrost.io/api/v0\" || 0,\n    nftPolicyId: \"placeholder_nft_policy_id\" || 0,\n    tokenPolicyId: \"placeholder_token_policy_id\" || 0,\n    scriptAddress: \"addr_test1placeholder_script_address\" || 0\n};\n// TREK Token configuration\nconst TREK_TOKEN = {\n    symbol: 'TREK',\n    decimals: 6,\n    policyId: BLOCKCHAIN_CONFIG.tokenPolicyId,\n    assetName: '54524b'\n};\n// Blockchain service class\nclass BlockchainService {\n    constructor(wallet = null){\n        this.wallet = null;\n        this.wallet = wallet;\n    }\n    setWallet(wallet) {\n        this.wallet = wallet;\n    }\n    // Check if Mesh SDK is loaded and ready\n    async ensureSDKLoaded() {\n        if (true) return false;\n        if (!meshLoaded) {\n            await loadMeshSDK();\n        }\n        return meshLoaded && BrowserWallet && Transaction && AssetMetadata;\n    }\n    // Get wallet balance in ADA\n    async getWalletBalance() {\n        if (!this.wallet || \"undefined\" === 'undefined') throw new Error('Wallet not connected');\n        const sdkReady = await this.ensureSDKLoaded();\n        if (!sdkReady) throw new Error('Blockchain SDK not loaded');\n        try {\n            const balance = await this.wallet.getBalance();\n            return balance;\n        } catch (error) {\n            console.error('Error fetching wallet balance:', error);\n            throw error;\n        }\n    }\n    // Get wallet assets (NFTs and tokens)\n    async getWalletAssets() {\n        if (!this.wallet) throw new Error('Wallet not connected');\n        try {\n            const assets = await this.wallet.getAssets();\n            return assets;\n        } catch (error) {\n            console.error('Error fetching wallet assets:', error);\n            throw error;\n        }\n    }\n    // Get TREK token balance\n    async getTrekTokenBalance() {\n        try {\n            const assets = await this.getWalletAssets();\n            const trekAsset = assets.find((asset)=>asset.unit.startsWith(TREK_TOKEN.policyId));\n            if (trekAsset) {\n                return parseInt(trekAsset.quantity) / Math.pow(10, TREK_TOKEN.decimals);\n            }\n            return 0;\n        } catch (error) {\n            console.error('Error fetching TREK token balance:', error);\n            return 0;\n        }\n    }\n    // Get trail NFTs owned by wallet\n    async getTrailNFTs() {\n        try {\n            const assets = await this.getWalletAssets();\n            const nfts = assets.filter((asset)=>asset.unit.startsWith(BLOCKCHAIN_CONFIG.nftPolicyId) && asset.quantity === '1');\n            // Fetch metadata for each NFT\n            const nftsWithMetadata = await Promise.all(nfts.map(async (nft)=>{\n                try {\n                    const metadata = await this.fetchNFTMetadata(nft.unit);\n                    return {\n                        ...nft,\n                        metadata\n                    };\n                } catch (error) {\n                    console.error(`Error fetching metadata for NFT ${nft.unit}:`, error);\n                    return {\n                        ...nft,\n                        metadata: null\n                    };\n                }\n            }));\n            return nftsWithMetadata;\n        } catch (error) {\n            console.error('Error fetching trail NFTs:', error);\n            return [];\n        }\n    }\n    // Fetch NFT metadata from blockchain\n    async fetchNFTMetadata(assetId) {\n        try {\n            const response = await fetch(`${BLOCKCHAIN_CONFIG.blockfrostUrl}/assets/${assetId}`, {\n                headers: {\n                    'project_id': BLOCKCHAIN_CONFIG.blockfrostApiKey\n                }\n            });\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            const assetData = await response.json();\n            if (assetData.onchain_metadata) {\n                return assetData.onchain_metadata;\n            }\n            return null;\n        } catch (error) {\n            console.error('Error fetching NFT metadata:', error);\n            return null;\n        }\n    }\n    // Create booking transaction\n    async createBookingTransaction(trailId, amount, date) {\n        if (!this.wallet || \"undefined\" === 'undefined' || 0) throw new Error('Wallet not connected or Transaction not available');\n        try {\n            const walletAddress = await this.wallet.getChangeAddress();\n            // Create booking metadata\n            const bookingMetadata = {\n                trail_id: trailId,\n                booking_date: date,\n                amount: amount,\n                timestamp: new Date().toISOString(),\n                hiker_address: walletAddress\n            };\n            // Build transaction (simplified - in production, this would interact with smart contracts)\n            const tx = new Transaction({\n                initiator: this.wallet\n            });\n            // Add metadata to transaction\n            tx.setMetadata(674, bookingMetadata);\n            // Send payment to script address (or trail operator)\n            tx.sendLovelace(BLOCKCHAIN_CONFIG.scriptAddress, (amount * 1000000).toString() // Convert ADA to Lovelace\n            );\n            const unsignedTx = await tx.build();\n            const signedTx = await this.wallet.signTx(unsignedTx);\n            const txHash = await this.wallet.submitTx(signedTx);\n            return txHash;\n        } catch (error) {\n            console.error('Error creating booking transaction:', error);\n            throw error;\n        }\n    }\n    // Mint trail completion NFT\n    async mintTrailNFT(trailData) {\n        if (!this.wallet || \"undefined\" === 'undefined' || 0) throw new Error('Wallet not connected or Transaction not available');\n        try {\n            const walletAddress = await this.wallet.getChangeAddress();\n            const completionDate = new Date().toISOString();\n            // Create NFT metadata\n            const metadata = {\n                name: `${trailData.trailName} Completion Certificate`,\n                description: `Proof of completion for ${trailData.trailName} trail in ${trailData.location}`,\n                image: `ipfs://QmTrailNFTImage${Date.now()}`,\n                attributes: {\n                    trail_name: trailData.trailName,\n                    location: trailData.location,\n                    difficulty: trailData.difficulty,\n                    completion_date: completionDate,\n                    coordinates: trailData.coordinates,\n                    hiker_address: walletAddress\n                }\n            };\n            // Generate unique asset name\n            const assetName = `VinTrekNFT${Date.now()}`;\n            // Build minting transaction (simplified)\n            const tx = new Transaction({\n                initiator: this.wallet\n            });\n            // Add minting logic here (requires smart contract integration)\n            // This is a placeholder - actual implementation would use Mesh SDK's minting functions\n            const unsignedTx = await tx.build();\n            const signedTx = await this.wallet.signTx(unsignedTx);\n            const txHash = await this.wallet.submitTx(signedTx);\n            return txHash;\n        } catch (error) {\n            console.error('Error minting trail NFT:', error);\n            throw error;\n        }\n    }\n    // Mint TREK tokens as rewards\n    async mintTrekTokens(amount, reason) {\n        if (!this.wallet || \"undefined\" === 'undefined' || 0) throw new Error('Wallet not connected or Transaction not available');\n        try {\n            const walletAddress = await this.wallet.getChangeAddress();\n            // Create reward metadata\n            const rewardMetadata = {\n                recipient: walletAddress,\n                amount: amount,\n                reason: reason,\n                timestamp: new Date().toISOString()\n            };\n            // Build token minting transaction (simplified)\n            const tx = new Transaction({\n                initiator: this.wallet\n            });\n            // Add metadata\n            tx.setMetadata(674, rewardMetadata);\n            // Add token minting logic here (requires smart contract integration)\n            const unsignedTx = await tx.build();\n            const signedTx = await this.wallet.signTx(unsignedTx);\n            const txHash = await this.wallet.submitTx(signedTx);\n            return txHash;\n        } catch (error) {\n            console.error('Error minting TREK tokens:', error);\n            throw error;\n        }\n    }\n    // Verify transaction on blockchain\n    async verifyTransaction(txHash) {\n        try {\n            const response = await fetch(`${BLOCKCHAIN_CONFIG.blockfrostUrl}/txs/${txHash}`, {\n                headers: {\n                    'project_id': BLOCKCHAIN_CONFIG.blockfrostApiKey\n                }\n            });\n            if (response.ok) {\n                const txData = await response.json();\n                return txData.block !== null // Transaction is confirmed if it's in a block\n                ;\n            }\n            return false;\n        } catch (error) {\n            console.error('Error verifying transaction:', error);\n            return false;\n        }\n    }\n}\n// Utility functions\nconst formatLovelaceToAda = (lovelace)=>{\n    return parseInt(lovelace) / 1000000;\n};\nconst formatAdaToLovelace = (ada)=>{\n    return (ada * 1000000).toString();\n};\nconst generateAssetName = (prefix)=>{\n    const timestamp = Date.now();\n    const random = Math.random().toString(36).substr(2, 9);\n    return `${prefix}${timestamp}${random}`;\n};\n// Export singleton instance\nconst blockchainService = new BlockchainService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/blockchain.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Chackothon%5CBlockchain%5CVintrek%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Chackothon%5CBlockchain%5CVintrek&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();