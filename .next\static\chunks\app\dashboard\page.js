/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/dashboard/page"],{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/createLucideIcon.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createLucideIcon$1),\n/* harmony export */   toKebabCase: () => (/* binding */ toKebabCase)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _defaultAttributes_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaultAttributes.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \n\nconst toKebabCase = (string)=>string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst createLucideIcon = (iconName, iconNode)=>{\n    const Component = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((param, ref)=>{\n        let { color = \"currentColor\", size = 24, strokeWidth = 2, absoluteStrokeWidth, children, ...rest } = param;\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"svg\", {\n            ref,\n            ..._defaultAttributes_mjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            width: size,\n            height: size,\n            stroke: color,\n            strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n            className: \"lucide lucide-\".concat(toKebabCase(iconName)),\n            ...rest\n        }, [\n            ...iconNode.map((param)=>{\n                let [tag, attrs] = param;\n                return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(tag, attrs);\n            }),\n            ...(Array.isArray(children) ? children : [\n                children\n            ]) || []\n        ]);\n    });\n    Component.displayName = \"\".concat(iconName);\n    return Component;\n};\nvar createLucideIcon$1 = createLucideIcon;\n //# sourceMappingURL=createLucideIcon.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/defaultAttributes.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ defaultAttributes)\n/* harmony export */ });\n/**\n * lucide-react v0.0.1 - ISC\n */ var defaultAttributes = {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 24,\n    height: 24,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: 2,\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n};\n //# sourceMappingURL=defaultAttributes.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vZGVmYXVsdEF0dHJpYnV0ZXMubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztJQUFBLENBQWU7SUFDYixLQUFPO0lBQ1AsS0FBTztJQUNQLE1BQVE7SUFDUixPQUFTO0lBQ1QsSUFBTTtJQUNOLE1BQVE7SUFDUixXQUFhO0lBQ2IsYUFBZTtJQUNmLGNBQWdCO0FBQ2xCIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxzcmNcXGRlZmF1bHRBdHRyaWJ1dGVzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcbiAgeG1sbnM6ICdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZycsXG4gIHdpZHRoOiAyNCxcbiAgaGVpZ2h0OiAyNCxcbiAgdmlld0JveDogJzAgMCAyNCAyNCcsXG4gIGZpbGw6ICdub25lJyxcbiAgc3Ryb2tlOiAnY3VycmVudENvbG9yJyxcbiAgc3Ryb2tlV2lkdGg6IDIsXG4gIHN0cm9rZUxpbmVjYXA6ICdyb3VuZCcsXG4gIHN0cm9rZUxpbmVqb2luOiAncm91bmQnLFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/arrow-left.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ArrowLeft)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst ArrowLeft = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ArrowLeft\", [\n    [\n        \"path\",\n        {\n            d: \"m12 19-7-7 7-7\",\n            key: \"1l729n\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M19 12H5\",\n            key: \"x3x0zl\"\n        }\n    ]\n]);\n //# sourceMappingURL=arrow-left.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvYXJyb3ctbGVmdC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFhTSxrQkFBWSxpRUFBZ0IsQ0FBQyxXQUFhO0lBQzlDO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxDQUFrQjtZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7SUFDL0M7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLENBQVk7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0NBQzFDIiwic291cmNlcyI6WyJEOlxcc3JjXFxpY29uc1xcYXJyb3ctbGVmdC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIEFycm93TGVmdFxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0p0TVRJZ01Ua3ROeTAzSURjdE55SWdMejRLSUNBOGNHRjBhQ0JrUFNKTk1Ua2dNVEpJTlNJZ0x6NEtQQzl6ZG1jK0NnPT0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2Fycm93LWxlZnRcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBBcnJvd0xlZnQgPSBjcmVhdGVMdWNpZGVJY29uKCdBcnJvd0xlZnQnLCBbXG4gIFsncGF0aCcsIHsgZDogJ20xMiAxOS03LTcgNy03Jywga2V5OiAnMWw3MjluJyB9XSxcbiAgWydwYXRoJywgeyBkOiAnTTE5IDEySDUnLCBrZXk6ICd4M3gwemwnIH1dLFxuXSk7XG5cbmV4cG9ydCBkZWZhdWx0IEFycm93TGVmdDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.mjs":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/coins.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Coins)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Coins = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Coins\", [\n    [\n        \"circle\",\n        {\n            cx: \"8\",\n            cy: \"8\",\n            r: \"6\",\n            key: \"3yglwk\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18.09 10.37A6 6 0 1 1 10.34 18\",\n            key: \"t5s6rm\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M7 6h1v4\",\n            key: \"1obek4\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m16.71 13.88.7.71-2.82 2.82\",\n            key: \"1rbuyh\"\n        }\n    ]\n]);\n //# sourceMappingURL=coins.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/map-pin.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MapPin)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst MapPin = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"MapPin\", [\n    [\n        \"path\",\n        {\n            d: \"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z\",\n            key: \"2oe9fu\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"10\",\n            r: \"3\",\n            key: \"ilqhr7\"\n        }\n    ]\n]);\n //# sourceMappingURL=map-pin.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mountain.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/mountain.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Mountain)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Mountain = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Mountain\", [\n    [\n        \"path\",\n        {\n            d: \"m8 3 4 8 5-5 5 15H2L8 3z\",\n            key: \"otkl63\"\n        }\n    ]\n]);\n //# sourceMappingURL=mountain.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbW91bnRhaW4ubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBYU0saUJBQVcsaUVBQWdCLENBQUMsVUFBWTtJQUM1QztRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBNEI7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0NBQzFEIiwic291cmNlcyI6WyJEOlxcc3JjXFxpY29uc1xcbW91bnRhaW4udHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBNb3VudGFpblxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0p0T0NBeklEUWdPQ0ExTFRVZ05TQXhOVWd5VERnZ00zb2lJQzgrQ2p3dmMzWm5QZ289KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9tb3VudGFpblxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IE1vdW50YWluID0gY3JlYXRlTHVjaWRlSWNvbignTW91bnRhaW4nLCBbXG4gIFsncGF0aCcsIHsgZDogJ204IDMgNCA4IDUtNSA1IDE1SDJMOCAzeicsIGtleTogJ290a2w2MycgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgTW91bnRhaW47XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mountain.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/trending-up.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TrendingUp)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst TrendingUp = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"TrendingUp\", [\n    [\n        \"polyline\",\n        {\n            points: \"22 7 13.5 15.5 8.5 10.5 2 17\",\n            key: \"126l90\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"16 7 22 7 22 13\",\n            key: \"kwv8wd\"\n        }\n    ]\n]);\n //# sourceMappingURL=trending-up.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/trophy.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Trophy)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Trophy = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Trophy\", [\n    [\n        \"path\",\n        {\n            d: \"M6 9H4.5a2.5 2.5 0 0 1 0-5H6\",\n            key: \"17hqa7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18 9h1.5a2.5 2.5 0 0 0 0-5H18\",\n            key: \"lmptdp\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M4 22h16\",\n            key: \"57wxv0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22\",\n            key: \"1nw9bq\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22\",\n            key: \"1np0yb\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18 2H6v7a6 6 0 0 0 12 0V2Z\",\n            key: \"u46fv3\"\n        }\n    ]\n]);\n //# sourceMappingURL=trophy.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(app-pages-browser)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q2hhY2tvdGhvbiU1QyU1Q0Jsb2NrY2hhaW4lNUMlNUNWaW50cmVrJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZGFzaGJvYXJkJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSxrTEFBc0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGhhY2tvdGhvblxcXFxCbG9ja2NoYWluXFxcXFZpbnRyZWtcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/client/app-dir/link.js ***!
  \*******************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    /**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */ default: function() {\n        return LinkComponent;\n    },\n    useLinkStatus: function() {\n        return useLinkStatus;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _formaturl = __webpack_require__(/*! ../../shared/lib/router/utils/format-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ../components/router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _usemergedref = __webpack_require__(/*! ../use-merged-ref */ \"(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js\");\nconst _utils = __webpack_require__(/*! ../../shared/lib/utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _warnonce = __webpack_require__(/*! ../../shared/lib/utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _links = __webpack_require__(/*! ../components/links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nconst _islocalurl = __webpack_require__(/*! ../../shared/lib/router/utils/is-local-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _approuterinstance = __webpack_require__(/*! ../components/app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _erroronce = __webpack_require__(/*! ../../shared/lib/utils/error-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\");\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute('target');\n    return target && target !== '_self' || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === 'A';\n    if (isAnchorNodeName && isModifiedEvent(e) || e.currentTarget.hasAttribute('download')) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    if (!(0, _islocalurl.isLocalURL)(href)) {\n        if (replace) {\n            // browser default behavior does not replace the history state\n            // so we need to do it manually\n            e.preventDefault();\n            location.replace(href);\n        }\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        if (onNavigate) {\n            let isDefaultPrevented = false;\n            onNavigate({\n                preventDefault: ()=>{\n                    isDefaultPrevented = true;\n                }\n            });\n            if (isDefaultPrevented) {\n                return;\n            }\n        }\n        (0, _approuterinstance.dispatchNavigateAction)(as || href, replace ? 'replace' : 'push', scroll != null ? scroll : true, linkInstanceRef.current);\n    };\n    _react.default.startTransition(navigate);\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === 'string') {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\nfunction LinkComponent(props) {\n    _s();\n    const [linkStatus, setOptimisticLinkStatus] = (0, _react.useOptimistic)(_links.IDLE_LINK_STATUS);\n    let children;\n    const linkInstanceRef = (0, _react.useRef)(null);\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, onNavigate, ref: forwardedRef, unstable_dynamicOnHover, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === 'string' || typeof children === 'number')) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const router = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    if (true) {\n        function createPropError(args) {\n            return Object.defineProperty(new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                value: \"E319\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === 'href') {\n                if (props[key] == null || typeof props[key] !== 'string' && typeof props[key] !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: props[key] === null ? 'null' : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            unstable_dynamicOnHover: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true,\n            onNavigate: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === 'as') {\n                if (props[key] && valType !== 'string' && valType !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'onClick' || key === 'onMouseEnter' || key === 'onTouchStart' || key === 'onNavigate') {\n                if (props[key] && valType !== 'function') {\n                    throw createPropError({\n                        key,\n                        expected: '`function`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'replace' || key === 'scroll' || key === 'shallow' || key === 'passHref' || key === 'prefetch' || key === 'legacyBehavior' || key === 'unstable_dynamicOnHover') {\n                if (props[key] != null && valType !== 'boolean') {\n                    throw createPropError({\n                        key,\n                        expected: '`boolean`',\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n    }\n    if (true) {\n        if (props.locale) {\n            (0, _warnonce.warnOnce)('The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization');\n        }\n        if (!asProp) {\n            let href;\n            if (typeof hrefProp === 'string') {\n                href = hrefProp;\n            } else if (typeof hrefProp === 'object' && typeof hrefProp.pathname === 'string') {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split('/').some((segment)=>segment.startsWith('[') && segment.endsWith(']'));\n                if (hasDynamicSegment) {\n                    throw Object.defineProperty(new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E267\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo({\n        \"LinkComponent.useMemo\": ()=>{\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n    }[\"LinkComponent.useMemo\"], [\n        hrefProp,\n        asProp\n    ]);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw Object.defineProperty(new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E320\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                throw Object.defineProperty(new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                    value: \"E266\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === 'a') {\n                throw Object.defineProperty(new Error('Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E209\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === 'object' && child.ref : forwardedRef;\n    // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n    // mount. In the future we will also use this to keep track of all the\n    // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n    // a revalidation or refresh.\n    const observeLinkVisibilityOnMount = _react.default.useCallback({\n        \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": (element)=>{\n            if (router !== null) {\n                linkInstanceRef.current = (0, _links.mountLinkInstance)(element, href, router, appPrefetchKind, prefetchEnabled, setOptimisticLinkStatus);\n            }\n            return ({\n                \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": ()=>{\n                    if (linkInstanceRef.current) {\n                        (0, _links.unmountLinkForCurrentNavigation)(linkInstanceRef.current);\n                        linkInstanceRef.current = null;\n                    }\n                    (0, _links.unmountPrefetchableInstance)(element);\n                }\n            })[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"];\n        }\n    }[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"], [\n        prefetchEnabled,\n        href,\n        router,\n        appPrefetchKind,\n        setOptimisticLinkStatus\n    ]);\n    const mergedRef = (0, _usemergedref.useMergedRef)(observeLinkVisibilityOnMount, childRef);\n    const childProps = {\n        ref: mergedRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw Object.defineProperty(new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E312\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n            if (!legacyBehavior && typeof onClick === 'function') {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === 'function') {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === 'function') {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled || \"development\" === 'development') {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === 'function') {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled) {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the basePath.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === 'a' && !('href' in child.props)) {\n        childProps.href = (0, _addbasepath.addBasePath)(as);\n    }\n    let link;\n    if (legacyBehavior) {\n        if (true) {\n            (0, _erroronce.errorOnce)('`legacyBehavior` is deprecated and will be removed in a future ' + 'release. A codemod is available to upgrade your components:\\n\\n' + 'npx @next/codemod@latest new-link .\\n\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components');\n        }\n        link = /*#__PURE__*/ _react.default.cloneElement(child, childProps);\n    } else {\n        link = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            ...restProps,\n            ...childProps,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(LinkStatusContext.Provider, {\n        value: linkStatus,\n        children: link\n    });\n}\n_s(LinkComponent, \"MNV6IdWv8Lu3MKc7Fm4v59uGRY0=\");\n_c = LinkComponent;\nconst LinkStatusContext = /*#__PURE__*/ (0, _react.createContext)(_links.IDLE_LINK_STATUS);\nconst useLinkStatus = ()=>{\n    return (0, _react.useContext)(LinkStatusContext);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c;\n$RefreshReg$(_c, \"LinkComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js":
/*!*********************************************************!*\
  !*** ./node_modules/next/dist/client/use-merged-ref.js ***!
  \*********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useMergedRef\", ({\n    enumerable: true,\n    get: function() {\n        return useMergedRef;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nfunction useMergedRef(refA, refB) {\n    const cleanupA = (0, _react.useRef)(null);\n    const cleanupB = (0, _react.useRef)(null);\n    // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n    // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n    // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n    // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n    // (because it hasn't been updated for React 19)\n    // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n    // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n    return (0, _react.useCallback)((current)=>{\n        if (current === null) {\n            const cleanupFnA = cleanupA.current;\n            if (cleanupFnA) {\n                cleanupA.current = null;\n                cleanupFnA();\n            }\n            const cleanupFnB = cleanupB.current;\n            if (cleanupFnB) {\n                cleanupB.current = null;\n                cleanupFnB();\n            }\n        } else {\n            if (refA) {\n                cleanupA.current = applyRef(refA, current);\n            }\n            if (refB) {\n                cleanupB.current = applyRef(refB, current);\n            }\n        }\n    }, [\n        refA,\n        refB\n    ]);\n}\nfunction applyRef(refA, current) {\n    if (typeof refA === 'function') {\n        const cleanup = refA(current);\n        if (typeof cleanup === 'function') {\n            return cleanup;\n        } else {\n            return ()=>refA(null);\n        }\n    } else {\n        refA.current = current;\n        return ()=>{\n            refA.current = null;\n        };\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-merged-ref.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkQ6XFxoYWNrb3Rob25cXEJsb2NrY2hhaW5cXFZpbnRyZWtcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxqc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/format-url.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    formatUrl: function() {\n        return formatUrl;\n    },\n    formatWithValidation: function() {\n        return formatWithValidation;\n    },\n    urlObjectKeys: function() {\n        return urlObjectKeys;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _querystring = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./querystring */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\"));\nconst slashedProtocols = /https?|ftp|gopher|file/;\nfunction formatUrl(urlObj) {\n    let { auth, hostname } = urlObj;\n    let protocol = urlObj.protocol || '';\n    let pathname = urlObj.pathname || '';\n    let hash = urlObj.hash || '';\n    let query = urlObj.query || '';\n    let host = false;\n    auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : '';\n    if (urlObj.host) {\n        host = auth + urlObj.host;\n    } else if (hostname) {\n        host = auth + (~hostname.indexOf(':') ? \"[\" + hostname + \"]\" : hostname);\n        if (urlObj.port) {\n            host += ':' + urlObj.port;\n        }\n    }\n    if (query && typeof query === 'object') {\n        query = String(_querystring.urlQueryToSearchParams(query));\n    }\n    let search = urlObj.search || query && \"?\" + query || '';\n    if (protocol && !protocol.endsWith(':')) protocol += ':';\n    if (urlObj.slashes || (!protocol || slashedProtocols.test(protocol)) && host !== false) {\n        host = '//' + (host || '');\n        if (pathname && pathname[0] !== '/') pathname = '/' + pathname;\n    } else if (!host) {\n        host = '';\n    }\n    if (hash && hash[0] !== '#') hash = '#' + hash;\n    if (search && search[0] !== '?') search = '?' + search;\n    pathname = pathname.replace(/[?#]/g, encodeURIComponent);\n    search = search.replace('#', '%23');\n    return \"\" + protocol + host + pathname + search + hash;\n}\nconst urlObjectKeys = [\n    'auth',\n    'hash',\n    'host',\n    'hostname',\n    'href',\n    'path',\n    'pathname',\n    'port',\n    'protocol',\n    'query',\n    'search',\n    'slashes'\n];\nfunction formatWithValidation(url) {\n    if (true) {\n        if (url !== null && typeof url === 'object') {\n            Object.keys(url).forEach((key)=>{\n                if (!urlObjectKeys.includes(key)) {\n                    console.warn(\"Unknown key passed via urlObject into url.format: \" + key);\n                }\n            });\n        }\n    }\n    return formatUrl(url);\n} //# sourceMappingURL=format-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js":
/*!************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/is-local-url.js ***!
  \************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isLocalURL\", ({\n    enumerable: true,\n    get: function() {\n        return isLocalURL;\n    }\n}));\nconst _utils = __webpack_require__(/*! ../../utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _hasbasepath = __webpack_require__(/*! ../../../../client/has-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\");\nfunction isLocalURL(url) {\n    // prevent a hydration mismatch on href for url with anchor refs\n    if (!(0, _utils.isAbsoluteUrl)(url)) return true;\n    try {\n        // absolute urls can be local if they are on the same origin\n        const locationOrigin = (0, _utils.getLocationOrigin)();\n        const resolved = new URL(url, locationOrigin);\n        return resolved.origin === locationOrigin && (0, _hasbasepath.hasBasePath)(resolved.pathname);\n    } catch (_) {\n        return false;\n    }\n} //# sourceMappingURL=is-local-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvaXMtbG9jYWwtdXJsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OENBTWdCQTs7O2VBQUFBOzs7bUNBTmlDO3lDQUNyQjtBQUtyQixTQUFTQSxXQUFXQyxHQUFXO0lBQ3BDLGdFQUFnRTtJQUNoRSxJQUFJLENBQUNDLENBQUFBLEdBQUFBLE9BQUFBLGFBQUFBLEVBQWNELE1BQU0sT0FBTztJQUNoQyxJQUFJO1FBQ0YsNERBQTREO1FBQzVELE1BQU1FLGlCQUFpQkMsQ0FBQUEsR0FBQUEsT0FBQUEsaUJBQUFBO1FBQ3ZCLE1BQU1DLFdBQVcsSUFBSUMsSUFBSUwsS0FBS0U7UUFDOUIsT0FBT0UsU0FBU0UsTUFBTSxLQUFLSixrQkFBa0JLLENBQUFBLEdBQUFBLGFBQUFBLFdBQUFBLEVBQVlILFNBQVNJLFFBQVE7SUFDNUUsRUFBRSxPQUFPQyxHQUFHO1FBQ1YsT0FBTztJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIkQ6XFxzcmNcXHNoYXJlZFxcbGliXFxyb3V0ZXJcXHV0aWxzXFxpcy1sb2NhbC11cmwudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXNBYnNvbHV0ZVVybCwgZ2V0TG9jYXRpb25PcmlnaW4gfSBmcm9tICcuLi8uLi91dGlscydcbmltcG9ydCB7IGhhc0Jhc2VQYXRoIH0gZnJvbSAnLi4vLi4vLi4vLi4vY2xpZW50L2hhcy1iYXNlLXBhdGgnXG5cbi8qKlxuICogRGV0ZWN0cyB3aGV0aGVyIGEgZ2l2ZW4gdXJsIGlzIHJvdXRhYmxlIGJ5IHRoZSBOZXh0LmpzIHJvdXRlciAoYnJvd3NlciBvbmx5KS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGlzTG9jYWxVUkwodXJsOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgLy8gcHJldmVudCBhIGh5ZHJhdGlvbiBtaXNtYXRjaCBvbiBocmVmIGZvciB1cmwgd2l0aCBhbmNob3IgcmVmc1xuICBpZiAoIWlzQWJzb2x1dGVVcmwodXJsKSkgcmV0dXJuIHRydWVcbiAgdHJ5IHtcbiAgICAvLyBhYnNvbHV0ZSB1cmxzIGNhbiBiZSBsb2NhbCBpZiB0aGV5IGFyZSBvbiB0aGUgc2FtZSBvcmlnaW5cbiAgICBjb25zdCBsb2NhdGlvbk9yaWdpbiA9IGdldExvY2F0aW9uT3JpZ2luKClcbiAgICBjb25zdCByZXNvbHZlZCA9IG5ldyBVUkwodXJsLCBsb2NhdGlvbk9yaWdpbilcbiAgICByZXR1cm4gcmVzb2x2ZWQub3JpZ2luID09PSBsb2NhdGlvbk9yaWdpbiAmJiBoYXNCYXNlUGF0aChyZXNvbHZlZC5wYXRobmFtZSlcbiAgfSBjYXRjaCAoXykge1xuICAgIHJldHVybiBmYWxzZVxuICB9XG59XG4iXSwibmFtZXMiOlsiaXNMb2NhbFVSTCIsInVybCIsImlzQWJzb2x1dGVVcmwiLCJsb2NhdGlvbk9yaWdpbiIsImdldExvY2F0aW9uT3JpZ2luIiwicmVzb2x2ZWQiLCJVUkwiLCJvcmlnaW4iLCJoYXNCYXNlUGF0aCIsInBhdGhuYW1lIiwiXyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/querystring.js ***!
  \***********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    assign: function() {\n        return assign;\n    },\n    searchParamsToUrlQuery: function() {\n        return searchParamsToUrlQuery;\n    },\n    urlQueryToSearchParams: function() {\n        return urlQueryToSearchParams;\n    }\n});\nfunction searchParamsToUrlQuery(searchParams) {\n    const query = {};\n    for (const [key, value] of searchParams.entries()){\n        const existing = query[key];\n        if (typeof existing === 'undefined') {\n            query[key] = value;\n        } else if (Array.isArray(existing)) {\n            existing.push(value);\n        } else {\n            query[key] = [\n                existing,\n                value\n            ];\n        }\n    }\n    return query;\n}\nfunction stringifyUrlQueryParam(param) {\n    if (typeof param === 'string') {\n        return param;\n    }\n    if (typeof param === 'number' && !isNaN(param) || typeof param === 'boolean') {\n        return String(param);\n    } else {\n        return '';\n    }\n}\nfunction urlQueryToSearchParams(query) {\n    const searchParams = new URLSearchParams();\n    for (const [key, value] of Object.entries(query)){\n        if (Array.isArray(value)) {\n            for (const item of value){\n                searchParams.append(key, stringifyUrlQueryParam(item));\n            }\n        } else {\n            searchParams.set(key, stringifyUrlQueryParam(value));\n        }\n    }\n    return searchParams;\n}\nfunction assign(target) {\n    for(var _len = arguments.length, searchParamsList = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        searchParamsList[_key - 1] = arguments[_key];\n    }\n    for (const searchParams of searchParamsList){\n        for (const key of searchParams.keys()){\n            target.delete(key);\n        }\n        for (const [key, value] of searchParams.entries()){\n            target.append(key, value);\n        }\n    }\n    return target;\n} //# sourceMappingURL=querystring.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils.js ***!
  \****************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DecodeError: function() {\n        return DecodeError;\n    },\n    MiddlewareNotFoundError: function() {\n        return MiddlewareNotFoundError;\n    },\n    MissingStaticPage: function() {\n        return MissingStaticPage;\n    },\n    NormalizeError: function() {\n        return NormalizeError;\n    },\n    PageNotFoundError: function() {\n        return PageNotFoundError;\n    },\n    SP: function() {\n        return SP;\n    },\n    ST: function() {\n        return ST;\n    },\n    WEB_VITALS: function() {\n        return WEB_VITALS;\n    },\n    execOnce: function() {\n        return execOnce;\n    },\n    getDisplayName: function() {\n        return getDisplayName;\n    },\n    getLocationOrigin: function() {\n        return getLocationOrigin;\n    },\n    getURL: function() {\n        return getURL;\n    },\n    isAbsoluteUrl: function() {\n        return isAbsoluteUrl;\n    },\n    isResSent: function() {\n        return isResSent;\n    },\n    loadGetInitialProps: function() {\n        return loadGetInitialProps;\n    },\n    normalizeRepeatedSlashes: function() {\n        return normalizeRepeatedSlashes;\n    },\n    stringifyError: function() {\n        return stringifyError;\n    }\n});\nconst WEB_VITALS = [\n    'CLS',\n    'FCP',\n    'FID',\n    'INP',\n    'LCP',\n    'TTFB'\n];\nfunction execOnce(fn) {\n    let used = false;\n    let result;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!used) {\n            used = true;\n            result = fn(...args);\n        }\n        return result;\n    };\n}\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nconst isAbsoluteUrl = (url)=>ABSOLUTE_URL_REGEX.test(url);\nfunction getLocationOrigin() {\n    const { protocol, hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? ':' + port : '');\n}\nfunction getURL() {\n    const { href } = window.location;\n    const origin = getLocationOrigin();\n    return href.substring(origin.length);\n}\nfunction getDisplayName(Component) {\n    return typeof Component === 'string' ? Component : Component.displayName || Component.name || 'Unknown';\n}\nfunction isResSent(res) {\n    return res.finished || res.headersSent;\n}\nfunction normalizeRepeatedSlashes(url) {\n    const urlParts = url.split('?');\n    const urlNoQuery = urlParts[0];\n    return urlNoQuery // first we replace any non-encoded backslashes with forward\n    // then normalize repeated forward slashes\n    .replace(/\\\\/g, '/').replace(/\\/\\/+/g, '/') + (urlParts[1] ? \"?\" + urlParts.slice(1).join('?') : '');\n}\nasync function loadGetInitialProps(App, ctx) {\n    if (true) {\n        var _App_prototype;\n        if ((_App_prototype = App.prototype) == null ? void 0 : _App_prototype.getInitialProps) {\n            const message = '\"' + getDisplayName(App) + '.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';\n            throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n                value: \"E394\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    }\n    // when called from _app `ctx` is nested in `ctx`\n    const res = ctx.res || ctx.ctx && ctx.ctx.res;\n    if (!App.getInitialProps) {\n        if (ctx.ctx && ctx.Component) {\n            // @ts-ignore pageProps default\n            return {\n                pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx)\n            };\n        }\n        return {};\n    }\n    const props = await App.getInitialProps(ctx);\n    if (res && isResSent(res)) {\n        return props;\n    }\n    if (!props) {\n        const message = '\"' + getDisplayName(App) + '.getInitialProps()\" should resolve to an object. But found \"' + props + '\" instead.';\n        throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    if (true) {\n        if (Object.keys(props).length === 0 && !ctx.ctx) {\n            console.warn(\"\" + getDisplayName(App) + \" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\");\n        }\n    }\n    return props;\n}\nconst SP = typeof performance !== 'undefined';\nconst ST = SP && [\n    'mark',\n    'measure',\n    'getEntriesByName'\n].every((method)=>typeof performance[method] === 'function');\nclass DecodeError extends Error {\n}\nclass NormalizeError extends Error {\n}\nclass PageNotFoundError extends Error {\n    constructor(page){\n        super();\n        this.code = 'ENOENT';\n        this.name = 'PageNotFoundError';\n        this.message = \"Cannot find module for page: \" + page;\n    }\n}\nclass MissingStaticPage extends Error {\n    constructor(page, message){\n        super();\n        this.message = \"Failed to load static file for page: \" + page + \" \" + message;\n    }\n}\nclass MiddlewareNotFoundError extends Error {\n    constructor(){\n        super();\n        this.code = 'ENOENT';\n        this.message = \"Cannot find the middleware module\";\n    }\n}\nfunction stringifyError(error) {\n    return JSON.stringify({\n        message: error.message,\n        stack: error.stack\n    });\n} //# sourceMappingURL=utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js":
/*!***************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils/error-once.js ***!
  \***************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"errorOnce\", ({\n    enumerable: true,\n    get: function() {\n        return errorOnce;\n    }\n}));\nlet errorOnce = (_)=>{};\nif (true) {\n    const errors = new Set();\n    errorOnce = (msg)=>{\n        if (!errors.has(msg)) {\n            console.error(msg);\n        }\n        errors.add(msg);\n    };\n} //# sourceMappingURL=error-once.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi91dGlscy9lcnJvci1vbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7NkNBV1NBOzs7ZUFBQUE7OztBQVhULElBQUlBLFlBQVksQ0FBQ0MsS0FBZTtBQUNoQyxJQUFJQyxJQUFvQixFQUFtQjtJQUN6QyxNQUFNRyxTQUFTLElBQUlDO0lBQ25CTixZQUFZLENBQUNPO1FBQ1gsSUFBSSxDQUFDRixPQUFPRyxHQUFHLENBQUNELE1BQU07WUFDcEJFLFFBQVFDLEtBQUssQ0FBQ0g7UUFDaEI7UUFDQUYsT0FBT00sR0FBRyxDQUFDSjtJQUNiO0FBQ0YiLCJzb3VyY2VzIjpbIkQ6XFxzcmNcXHNoYXJlZFxcbGliXFx1dGlsc1xcZXJyb3Itb25jZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgZXJyb3JPbmNlID0gKF86IHN0cmluZykgPT4ge31cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIGNvbnN0IGVycm9ycyA9IG5ldyBTZXQ8c3RyaW5nPigpXG4gIGVycm9yT25jZSA9IChtc2c6IHN0cmluZykgPT4ge1xuICAgIGlmICghZXJyb3JzLmhhcyhtc2cpKSB7XG4gICAgICBjb25zb2xlLmVycm9yKG1zZylcbiAgICB9XG4gICAgZXJyb3JzLmFkZChtc2cpXG4gIH1cbn1cblxuZXhwb3J0IHsgZXJyb3JPbmNlIH1cbiJdLCJuYW1lcyI6WyJlcnJvck9uY2UiLCJfIiwicHJvY2VzcyIsImVudiIsIk5PREVfRU5WIiwiZXJyb3JzIiwiU2V0IiwibXNnIiwiaGFzIiwiY29uc29sZSIsImVycm9yIiwiYWRkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Coins,MapPin,Mountain,TrendingUp,Trophy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Coins,MapPin,Mountain,TrendingUp,Trophy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mountain.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Coins,MapPin,Mountain,TrendingUp,Trophy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Coins,MapPin,Mountain,TrendingUp,Trophy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Coins,MapPin,Mountain,TrendingUp,Trophy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Coins,MapPin,Mountain,TrendingUp,Trophy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/providers/WalletProvider */ \"(app-pages-browser)/./src/components/providers/WalletProvider.tsx\");\n/* harmony import */ var _hooks_useTrailData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useTrailData */ \"(app-pages-browser)/./src/hooks/useTrailData.ts\");\n/* harmony import */ var _lib_trailUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/trailUtils */ \"(app-pages-browser)/./src/lib/trailUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { connected } = (0,_components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_3__.useWallet)();\n    const { completedTrails, userStats, recordings } = (0,_hooks_useTrailData__WEBPACK_IMPORTED_MODULE_4__.useTrailData)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    if (!connected) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-green-50 to-blue-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"bg-white shadow-sm border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center h-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 27,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"VinTrek\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto px-4 py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-lg p-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-16 w-16 text-gray-300 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                children: \"Wallet Required\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: \"Please connect your Cardano wallet to view your trail dashboard.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors inline-block\",\n                                children: \"Connect Wallet\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-50 to-blue-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"VinTrek\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: \"Trail Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Track your progress and view your hiking achievements\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-8 px-6\",\n                                    children: [\n                                        {\n                                            id: 'overview',\n                                            label: 'Overview',\n                                            icon: _barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                                        },\n                                        {\n                                            id: 'trails',\n                                            label: 'Completed Trails',\n                                            icon: _barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                                        },\n                                        {\n                                            id: 'recordings',\n                                            label: 'Recordings',\n                                            icon: _barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                                        },\n                                        {\n                                            id: 'achievements',\n                                            label: 'Achievements',\n                                            icon: _barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                                        }\n                                    ].map((param)=>{\n                                        let { id, label, icon: Icon } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setActiveTab(id),\n                                            className: \"flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === id ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: label\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, id, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 md:grid-cols-4 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 115,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-green-800\",\n                                                                        children: \"Total Trails\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 116,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 114,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-green-900\",\n                                                                children: userStats.totalTrails\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 118,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 123,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-blue-800\",\n                                                                        children: \"Distance\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 124,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 122,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-blue-900\",\n                                                                children: (0,_lib_trailUtils__WEBPACK_IMPORTED_MODULE_5__.formatDistance)(userStats.totalDistance)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-purple-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 133,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-purple-800\",\n                                                                        children: \"Time\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 134,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 132,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-purple-900\",\n                                                                children: (0,_lib_trailUtils__WEBPACK_IMPORTED_MODULE_5__.formatDuration)(userStats.totalDuration)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 136,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-yellow-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 143,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-yellow-800\",\n                                                                        children: \"TREK Tokens\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 144,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-yellow-900\",\n                                                                children: userStats.trekTokensEarned\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 146,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                        children: \"Recent Trails\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    completedTrails.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8 text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"No completed trails yet\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/record\",\n                                                                className: \"text-green-600 hover:text-green-700 font-medium\",\n                                                                children: \"Start your first trail →\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: completedTrails.slice(0, 5).map((trail)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-50 rounded-lg p-4 flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: trail.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 169,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: (0,_lib_trailUtils__WEBPACK_IMPORTED_MODULE_5__.formatDistance)(trail.distance)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                        lineNumber: 171,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: (0,_lib_trailUtils__WEBPACK_IMPORTED_MODULE_5__.formatDuration)(trail.duration)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                        lineNumber: 172,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"capitalize\",\n                                                                                        children: trail.difficulty\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                        lineNumber: 173,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 170,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 168,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: new Date(trail.completedAt).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 176,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, trail.id, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeTab === 'trails' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                children: [\n                                                    \"Completed Trails (\",\n                                                    completedTrails.length,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this),\n                                            completedTrails.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-12 text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-16 w-16 mx-auto mb-4 text-gray-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg mb-2\",\n                                                        children: \"No completed trails yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mb-4\",\n                                                        children: \"Start recording your first trail to see it here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/record\",\n                                                        className: \"bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors inline-block\",\n                                                        children: \"Record Trail\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-4\",\n                                                children: completedTrails.map((trail)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 rounded-lg p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start justify-between mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-lg font-semibold text-gray-900\",\n                                                                                children: trail.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 210,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: trail.location\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 211,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 209,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 py-1 rounded text-xs font-medium \".concat(trail.difficulty === 'Easy' ? 'bg-green-100 text-green-800' : trail.difficulty === 'Moderate' ? 'bg-yellow-100 text-yellow-800' : trail.difficulty === 'Hard' ? 'bg-orange-100 text-orange-800' : 'bg-red-100 text-red-800'),\n                                                                                children: trail.difficulty\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 214,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            trail.nftMinted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs font-medium\",\n                                                                                children: \"NFT Minted\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 223,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 213,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Distance\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 232,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: (0,_lib_trailUtils__WEBPACK_IMPORTED_MODULE_5__.formatDistance)(trail.distance)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 233,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 231,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Duration\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 236,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: (0,_lib_trailUtils__WEBPACK_IMPORTED_MODULE_5__.formatDuration)(trail.duration)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 237,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 235,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"TREK Tokens\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 240,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: trail.trekTokensEarned\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 241,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 239,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Completed\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 244,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: new Date(trail.completedAt).toLocaleDateString()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 245,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            trail.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 p-3 bg-white rounded border\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: \"Notes\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 253,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm\",\n                                                                        children: trail.notes\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 254,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, trail.id, true, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeTab === 'recordings' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                children: [\n                                                    \"Trail Recordings (\",\n                                                    recordings.length,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this),\n                                            recordings.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-12 text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-16 w-16 mx-auto mb-4 text-gray-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg mb-2\",\n                                                        children: \"No recordings found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mb-4\",\n                                                        children: \"Your trail recordings will appear here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-4\",\n                                                children: recordings.map((recording)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 rounded-lg p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start justify-between mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-lg font-semibold text-gray-900\",\n                                                                                children: recording.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 281,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            recording.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: recording.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 283,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 rounded text-xs font-medium \".concat(recording.isActive ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                                                            children: recording.isActive ? 'Active' : 'Completed'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 287,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Distance\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 298,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: (0,_lib_trailUtils__WEBPACK_IMPORTED_MODULE_5__.formatDistance)(recording.totalDistance)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 299,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 297,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Duration\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 302,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: (0,_lib_trailUtils__WEBPACK_IMPORTED_MODULE_5__.formatDuration)(recording.totalDuration)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 303,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Points\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 306,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: recording.coordinates.length\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 307,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Started\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 310,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: new Date(recording.startTime).toLocaleDateString()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 311,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 309,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, recording.id, true, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeTab === 'achievements' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                children: [\n                                                    \"Achievements (\",\n                                                    userStats.achievements.length,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this),\n                                            userStats.achievements.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-12 text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-16 w-16 mx-auto mb-4 text-gray-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg mb-2\",\n                                                        children: \"No achievements yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mb-4\",\n                                                        children: \"Complete trails to unlock achievements\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid md:grid-cols-2 gap-4\",\n                                                children: userStats.achievements.map((achievement)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg p-4 border border-yellow-200\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 bg-yellow-200 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Coins_MapPin_Mountain_TrendingUp_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-6 w-6 text-yellow-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-semibold text-yellow-900\",\n                                                                            children: achievement.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 343,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-yellow-700\",\n                                                                            children: achievement.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 344,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-yellow-600 capitalize\",\n                                                                            children: achievement.category\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 345,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, achievement.id, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"ALEVkz4iHDKUk7Beu/hAjKVRZWY=\", false, function() {\n    return [\n        _components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_3__.useWallet,\n        _hooks_useTrailData__WEBPACK_IMPORTED_MODULE_4__.useTrailData\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers/WalletProvider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/WalletProvider.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletProvider: () => (/* binding */ WalletProvider),\n/* harmony export */   useWallet: () => (/* binding */ useWallet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useWallet,WalletProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst WalletContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useWallet() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(WalletContext);\n    if (context === undefined) {\n        throw new Error('useWallet must be used within a WalletProvider');\n    }\n    return context;\n}\n_s(useWallet, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction WalletProvider(param) {\n    let { children } = param;\n    _s1();\n    const [wallet, setWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connected, setConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [connecting, setConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [address, setAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [balance, setBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getAvailableWallets = ()=>{\n        var _window_cardano, _window_cardano1, _window_cardano2, _window_cardano3, _window_cardano4, _window_cardano5, _window_cardano6, _window_cardano7;\n        if (false) {}\n        const wallets = [];\n        // Check for common Cardano wallets\n        if ((_window_cardano = window.cardano) === null || _window_cardano === void 0 ? void 0 : _window_cardano.lace) wallets.push('lace');\n        if ((_window_cardano1 = window.cardano) === null || _window_cardano1 === void 0 ? void 0 : _window_cardano1.eternl) wallets.push('eternl');\n        if ((_window_cardano2 = window.cardano) === null || _window_cardano2 === void 0 ? void 0 : _window_cardano2.nami) wallets.push('nami');\n        if ((_window_cardano3 = window.cardano) === null || _window_cardano3 === void 0 ? void 0 : _window_cardano3.flint) wallets.push('flint');\n        if ((_window_cardano4 = window.cardano) === null || _window_cardano4 === void 0 ? void 0 : _window_cardano4.typhon) wallets.push('typhon');\n        if ((_window_cardano5 = window.cardano) === null || _window_cardano5 === void 0 ? void 0 : _window_cardano5.gerowallet) wallets.push('gerowallet');\n        if ((_window_cardano6 = window.cardano) === null || _window_cardano6 === void 0 ? void 0 : _window_cardano6.ccvault) wallets.push('ccvault');\n        if ((_window_cardano7 = window.cardano) === null || _window_cardano7 === void 0 ? void 0 : _window_cardano7.yoroi) wallets.push('yoroi');\n        // Debug logging\n        console.log('Checking for wallets...');\n        console.log('window.cardano:', window.cardano);\n        console.log('Available wallets found:', wallets);\n        return wallets;\n    };\n    const connect = async (walletName)=>{\n        if (false) {}\n        console.log(\"Attempting to connect to \".concat(walletName, \" wallet...\"));\n        setConnecting(true);\n        try {\n            // Check if cardano object exists\n            if (!window.cardano) {\n                throw new Error('No Cardano wallets detected. Please install a Cardano wallet extension.');\n            }\n            // Check if specific wallet is available\n            if (!window.cardano[walletName]) {\n                throw new Error(\"\".concat(walletName, \" wallet not found. Please install \").concat(walletName, \" wallet extension.\"));\n            }\n            console.log(\"Found \".concat(walletName, \" wallet, attempting to enable...\"));\n            // Enable the wallet\n            const walletApi = await window.cardano[walletName].enable();\n            console.log('Wallet enabled successfully:', walletApi);\n            setWallet(walletApi);\n            // Get wallet address\n            try {\n                const addresses = await walletApi.getUsedAddresses();\n                console.log('Fetched addresses:', addresses);\n                if (addresses.length > 0) {\n                    setAddress(addresses[0]);\n                } else {\n                    // Try to get unused addresses if no used addresses\n                    const unusedAddresses = await walletApi.getUnusedAddresses();\n                    if (unusedAddresses.length > 0) {\n                        setAddress(unusedAddresses[0]);\n                    }\n                }\n            } catch (error) {\n                console.warn('Could not fetch addresses:', error);\n            }\n            // Get wallet balance\n            try {\n                const balance = await walletApi.getBalance();\n                console.log('Fetched balance:', balance);\n                setBalance(balance);\n            } catch (error) {\n                console.warn('Could not fetch balance:', error);\n                setBalance('0');\n            }\n            setConnected(true);\n            console.log(\"Successfully connected to \".concat(walletName));\n            // Store connection state\n            localStorage.setItem('vintrek_wallet', walletName);\n        } catch (error) {\n            console.error('Failed to connect wallet:', error);\n            throw error;\n        } finally{\n            setConnecting(false);\n        }\n    };\n    const disconnect = ()=>{\n        setWallet(null);\n        setConnected(false);\n        setAddress(null);\n        setBalance(null);\n        localStorage.removeItem('vintrek_wallet');\n    };\n    // Auto-reconnect on page load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletProvider.useEffect\": ()=>{\n            const savedWallet = localStorage.getItem('vintrek_wallet');\n            if (savedWallet && getAvailableWallets().includes(savedWallet)) {\n                connect(savedWallet).catch(console.error);\n            }\n        }\n    }[\"WalletProvider.useEffect\"], []);\n    const value = {\n        wallet,\n        connected,\n        connecting,\n        address,\n        balance,\n        connect,\n        disconnect,\n        getAvailableWallets\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\providers\\\\WalletProvider.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n_s1(WalletProvider, \"dSBI9X6BQq4oMvol2/Gn4R+d8Wg=\");\n_c = WalletProvider;\nvar _c;\n$RefreshReg$(_c, \"WalletProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/WalletProvider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useTrailData.ts":
/*!***********************************!*\
  !*** ./src/hooks/useTrailData.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTrailData: () => (/* binding */ useTrailData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/trailStorage */ \"(app-pages-browser)/./src/lib/trailStorage.ts\");\n/* harmony import */ var _components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/WalletProvider */ \"(app-pages-browser)/./src/components/providers/WalletProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ useTrailData auto */ \n\n\nfunction useTrailData() {\n    const { address } = (0,_components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__.useWallet)();\n    const [recordings, setRecordings] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [completedTrails, setCompletedTrails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [userStats, setUserStats] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        totalTrails: 0,\n        totalDistance: 0,\n        totalDuration: 0,\n        totalElevationGain: 0,\n        trekTokensEarned: 0,\n        nftsMinted: 0,\n        achievements: []\n    });\n    const [storageUsage, setStorageUsage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        used: 0,\n        total: 0,\n        percentage: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Load data from storage\n    const loadData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTrailData.useCallback[loadData]\": ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                // Load recordings\n                const loadedRecordings = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.getTrailRecordings();\n                setRecordings(loadedRecordings);\n                // Load completed trails for current user\n                if (address) {\n                    const loadedTrails = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.getCompletedTrails(address);\n                    setCompletedTrails(loadedTrails);\n                    const loadedStats = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.getUserStats(address);\n                    setUserStats(loadedStats);\n                } else {\n                    setCompletedTrails([]);\n                    setUserStats({\n                        totalTrails: 0,\n                        totalDistance: 0,\n                        totalDuration: 0,\n                        totalElevationGain: 0,\n                        trekTokensEarned: 0,\n                        nftsMinted: 0,\n                        achievements: []\n                    });\n                }\n                // Load storage usage\n                const usage = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.getStorageUsage();\n                setStorageUsage(usage);\n            } catch (err) {\n                setError(err instanceof Error ? err.message : 'Failed to load trail data');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useTrailData.useCallback[loadData]\"], [\n        address\n    ]);\n    // Save recording\n    const saveRecording = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTrailData.useCallback[saveRecording]\": (recording)=>{\n            try {\n                const success = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.saveTrailRecording(recording);\n                if (success) {\n                    setRecordings({\n                        \"useTrailData.useCallback[saveRecording]\": (prev)=>{\n                            const existing = prev.findIndex({\n                                \"useTrailData.useCallback[saveRecording].existing\": (r)=>r.id === recording.id\n                            }[\"useTrailData.useCallback[saveRecording].existing\"]);\n                            if (existing >= 0) {\n                                const updated = [\n                                    ...prev\n                                ];\n                                updated[existing] = recording;\n                                return updated;\n                            } else {\n                                return [\n                                    ...prev,\n                                    recording\n                                ];\n                            }\n                        }\n                    }[\"useTrailData.useCallback[saveRecording]\"]);\n                    // Update storage usage\n                    setStorageUsage(_lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.getStorageUsage());\n                }\n                return success;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : 'Failed to save recording');\n                return false;\n            }\n        }\n    }[\"useTrailData.useCallback[saveRecording]\"], []);\n    // Delete recording\n    const deleteRecording = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTrailData.useCallback[deleteRecording]\": (id)=>{\n            try {\n                const success = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.deleteTrailRecording(id);\n                if (success) {\n                    setRecordings({\n                        \"useTrailData.useCallback[deleteRecording]\": (prev)=>prev.filter({\n                                \"useTrailData.useCallback[deleteRecording]\": (r)=>r.id !== id\n                            }[\"useTrailData.useCallback[deleteRecording]\"])\n                    }[\"useTrailData.useCallback[deleteRecording]\"]);\n                    setStorageUsage(_lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.getStorageUsage());\n                }\n                return success;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : 'Failed to delete recording');\n                return false;\n            }\n        }\n    }[\"useTrailData.useCallback[deleteRecording]\"], []);\n    // Get recording by ID\n    const getRecording = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTrailData.useCallback[getRecording]\": (id)=>{\n            return recordings.find({\n                \"useTrailData.useCallback[getRecording]\": (r)=>r.id === id\n            }[\"useTrailData.useCallback[getRecording]\"]) || null;\n        }\n    }[\"useTrailData.useCallback[getRecording]\"], [\n        recordings\n    ]);\n    // Save completed trail\n    const saveCompletedTrail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTrailData.useCallback[saveCompletedTrail]\": (trail)=>{\n            if (!address) {\n                setError('Wallet not connected');\n                return false;\n            }\n            try {\n                const trailWithAddress = {\n                    ...trail,\n                    walletAddress: address\n                };\n                const success = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.saveCompletedTrail(trailWithAddress);\n                if (success) {\n                    setCompletedTrails({\n                        \"useTrailData.useCallback[saveCompletedTrail]\": (prev)=>{\n                            const existing = prev.findIndex({\n                                \"useTrailData.useCallback[saveCompletedTrail].existing\": (t)=>t.id === trail.id\n                            }[\"useTrailData.useCallback[saveCompletedTrail].existing\"]);\n                            if (existing >= 0) {\n                                const updated = [\n                                    ...prev\n                                ];\n                                updated[existing] = trailWithAddress;\n                                return updated;\n                            } else {\n                                return [\n                                    ...prev,\n                                    trailWithAddress\n                                ];\n                            }\n                        }\n                    }[\"useTrailData.useCallback[saveCompletedTrail]\"]);\n                    // Refresh stats\n                    const updatedStats = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.getUserStats(address);\n                    setUserStats(updatedStats);\n                    // Update storage usage\n                    setStorageUsage(_lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.getStorageUsage());\n                }\n                return success;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : 'Failed to save completed trail');\n                return false;\n            }\n        }\n    }[\"useTrailData.useCallback[saveCompletedTrail]\"], [\n        address\n    ]);\n    // Get completed trail by ID\n    const getCompletedTrail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTrailData.useCallback[getCompletedTrail]\": (id)=>{\n            return completedTrails.find({\n                \"useTrailData.useCallback[getCompletedTrail]\": (t)=>t.id === id\n            }[\"useTrailData.useCallback[getCompletedTrail]\"]) || null;\n        }\n    }[\"useTrailData.useCallback[getCompletedTrail]\"], [\n        completedTrails\n    ]);\n    // Refresh stats\n    const refreshStats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTrailData.useCallback[refreshStats]\": ()=>{\n            if (address) {\n                const stats = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.getUserStats(address);\n                setUserStats(stats);\n            }\n        }\n    }[\"useTrailData.useCallback[refreshStats]\"], [\n        address\n    ]);\n    // Export data\n    const exportData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTrailData.useCallback[exportData]\": ()=>{\n            if (!address) {\n                throw new Error('Wallet not connected');\n            }\n            return _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.exportTrailData(address);\n        }\n    }[\"useTrailData.useCallback[exportData]\"], [\n        address\n    ]);\n    // Import data\n    const importData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTrailData.useCallback[importData]\": (jsonData)=>{\n            try {\n                const success = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.importTrailData(jsonData);\n                if (success) {\n                    loadData() // Reload all data\n                    ;\n                }\n                return success;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : 'Failed to import data');\n                return false;\n            }\n        }\n    }[\"useTrailData.useCallback[importData]\"], [\n        loadData\n    ]);\n    // Clear all data\n    const clearAllData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTrailData.useCallback[clearAllData]\": ()=>{\n            try {\n                const success = _lib_trailStorage__WEBPACK_IMPORTED_MODULE_1__.TrailStorage.clearAllData();\n                if (success) {\n                    setRecordings([]);\n                    setCompletedTrails([]);\n                    setUserStats({\n                        totalTrails: 0,\n                        totalDistance: 0,\n                        totalDuration: 0,\n                        totalElevationGain: 0,\n                        trekTokensEarned: 0,\n                        nftsMinted: 0,\n                        achievements: []\n                    });\n                    setStorageUsage({\n                        used: 0,\n                        total: 0,\n                        percentage: 0\n                    });\n                }\n                return success;\n            } catch (err) {\n                setError(err instanceof Error ? err.message : 'Failed to clear data');\n                return false;\n            }\n        }\n    }[\"useTrailData.useCallback[clearAllData]\"], []);\n    // Load data when component mounts or address changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTrailData.useEffect\": ()=>{\n            loadData();\n        }\n    }[\"useTrailData.useEffect\"], [\n        loadData\n    ]);\n    return {\n        recordings,\n        saveRecording,\n        deleteRecording,\n        getRecording,\n        completedTrails,\n        saveCompletedTrail,\n        getCompletedTrail,\n        userStats,\n        refreshStats,\n        exportData,\n        importData,\n        clearAllData,\n        storageUsage,\n        loading,\n        error\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useTrailData.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/trailStorage.ts":
/*!*********************************!*\
  !*** ./src/lib/trailStorage.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrailStorage: () => (/* binding */ TrailStorage)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ TrailStorage auto */ // Storage keys\nconst STORAGE_KEYS = {\n    TRAIL_RECORDINGS: 'vintrek_trail_recordings',\n    COMPLETED_TRAILS: 'vintrek_completed_trails',\n    USER_STATS: 'vintrek_user_stats',\n    TRAIL_TEMPLATES: 'vintrek_trail_templates',\n    OFFLINE_DATA: 'vintrek_offline_data'\n};\n// Trail Recording Storage\nclass TrailStorage {\n    // Save trail recording\n    static saveTrailRecording(recording) {\n        try {\n            const recordings = this.getTrailRecordings();\n            const existingIndex = recordings.findIndex((r)=>r.id === recording.id);\n            if (existingIndex >= 0) {\n                recordings[existingIndex] = recording;\n            } else {\n                recordings.push(recording);\n            }\n            localStorage.setItem(STORAGE_KEYS.TRAIL_RECORDINGS, JSON.stringify(recordings));\n            return true;\n        } catch (error) {\n            console.error('Failed to save trail recording:', error);\n            return false;\n        }\n    }\n    // Get all trail recordings\n    static getTrailRecordings() {\n        try {\n            const data = localStorage.getItem(STORAGE_KEYS.TRAIL_RECORDINGS);\n            return data ? JSON.parse(data) : [];\n        } catch (error) {\n            console.error('Failed to get trail recordings:', error);\n            return [];\n        }\n    }\n    // Get trail recording by ID\n    static getTrailRecording(id) {\n        const recordings = this.getTrailRecordings();\n        return recordings.find((r)=>r.id === id) || null;\n    }\n    // Delete trail recording\n    static deleteTrailRecording(id) {\n        try {\n            const recordings = this.getTrailRecordings();\n            const filtered = recordings.filter((r)=>r.id !== id);\n            localStorage.setItem(STORAGE_KEYS.TRAIL_RECORDINGS, JSON.stringify(filtered));\n            return true;\n        } catch (error) {\n            console.error('Failed to delete trail recording:', error);\n            return false;\n        }\n    }\n    // Save completed trail\n    static saveCompletedTrail(trail) {\n        try {\n            const trails = this.getCompletedTrails();\n            const existingIndex = trails.findIndex((t)=>t.id === trail.id);\n            if (existingIndex >= 0) {\n                trails[existingIndex] = trail;\n            } else {\n                trails.push(trail);\n            }\n            localStorage.setItem(STORAGE_KEYS.COMPLETED_TRAILS, JSON.stringify(trails));\n            this.updateUserStats(trail);\n            return true;\n        } catch (error) {\n            console.error('Failed to save completed trail:', error);\n            return false;\n        }\n    }\n    // Get completed trails\n    static getCompletedTrails(walletAddress) {\n        try {\n            const data = localStorage.getItem(STORAGE_KEYS.COMPLETED_TRAILS);\n            const trails = data ? JSON.parse(data) : [];\n            if (walletAddress) {\n                return trails.filter((t)=>t.walletAddress === walletAddress);\n            }\n            return trails;\n        } catch (error) {\n            console.error('Failed to get completed trails:', error);\n            return [];\n        }\n    }\n    // Get completed trail by ID\n    static getCompletedTrail(id) {\n        const trails = this.getCompletedTrails();\n        return trails.find((t)=>t.id === id) || null;\n    }\n    // Update user statistics\n    static updateUserStats(completedTrail) {\n        try {\n            const stats = this.getUserStats(completedTrail.walletAddress);\n            const updatedStats = {\n                totalTrails: stats.totalTrails + 1,\n                totalDistance: stats.totalDistance + completedTrail.distance,\n                totalDuration: stats.totalDuration + completedTrail.duration,\n                totalElevationGain: stats.totalElevationGain + completedTrail.coordinates.reduce((gain, coord, index)=>{\n                    if (index === 0 || !coord.altitude) return gain;\n                    const prevCoord = completedTrail.coordinates[index - 1];\n                    if (!prevCoord.altitude) return gain;\n                    const diff = coord.altitude - prevCoord.altitude;\n                    return gain + (diff > 0 ? diff : 0);\n                }, 0),\n                trekTokensEarned: stats.trekTokensEarned + completedTrail.trekTokensEarned,\n                nftsMinted: stats.nftsMinted + (completedTrail.nftMinted ? 1 : 0),\n                favoriteTrail: this.getFavoriteTrail(completedTrail.walletAddress),\n                longestTrail: this.getLongestTrail(completedTrail.walletAddress),\n                achievements: this.calculateAchievements(completedTrail.walletAddress)\n            };\n            localStorage.setItem(\"\".concat(STORAGE_KEYS.USER_STATS, \"_\").concat(completedTrail.walletAddress), JSON.stringify(updatedStats));\n        } catch (error) {\n            console.error('Failed to update user stats:', error);\n        }\n    }\n    // Get user statistics\n    static getUserStats(walletAddress) {\n        try {\n            const data = localStorage.getItem(\"\".concat(STORAGE_KEYS.USER_STATS, \"_\").concat(walletAddress));\n            return data ? JSON.parse(data) : {\n                totalTrails: 0,\n                totalDistance: 0,\n                totalDuration: 0,\n                totalElevationGain: 0,\n                trekTokensEarned: 0,\n                nftsMinted: 0,\n                achievements: []\n            };\n        } catch (error) {\n            console.error('Failed to get user stats:', error);\n            return {\n                totalTrails: 0,\n                totalDistance: 0,\n                totalDuration: 0,\n                totalElevationGain: 0,\n                trekTokensEarned: 0,\n                nftsMinted: 0,\n                achievements: []\n            };\n        }\n    }\n    // Get favorite trail (most completed)\n    static getFavoriteTrail(walletAddress) {\n        var _Object_entries_sort_;\n        const trails = this.getCompletedTrails(walletAddress);\n        const trailCounts = trails.reduce((counts, trail)=>{\n            counts[trail.name] = (counts[trail.name] || 0) + 1;\n            return counts;\n        }, {});\n        return (_Object_entries_sort_ = Object.entries(trailCounts).sort((param, param1)=>{\n            let [, a] = param, [, b] = param1;\n            return b - a;\n        })[0]) === null || _Object_entries_sort_ === void 0 ? void 0 : _Object_entries_sort_[0];\n    }\n    // Get longest trail\n    static getLongestTrail(walletAddress) {\n        var _trails_sort_;\n        const trails = this.getCompletedTrails(walletAddress);\n        return (_trails_sort_ = trails.sort((a, b)=>b.distance - a.distance)[0]) === null || _trails_sort_ === void 0 ? void 0 : _trails_sort_.name;\n    }\n    // Calculate achievements\n    static calculateAchievements(walletAddress) {\n        const trails = this.getCompletedTrails(walletAddress);\n        const stats = this.getUserStats(walletAddress);\n        const achievements = [];\n        // Distance achievements\n        if (stats.totalDistance >= 100000) achievements.push({\n            id: 'distance_100k',\n            name: '100km Explorer',\n            category: 'distance'\n        });\n        if (stats.totalDistance >= 50000) achievements.push({\n            id: 'distance_50k',\n            name: '50km Adventurer',\n            category: 'distance'\n        });\n        if (stats.totalDistance >= 10000) achievements.push({\n            id: 'distance_10k',\n            name: '10km Hiker',\n            category: 'distance'\n        });\n        // Trail count achievements\n        if (stats.totalTrails >= 50) achievements.push({\n            id: 'trails_50',\n            name: 'Trail Master',\n            category: 'trails'\n        });\n        if (stats.totalTrails >= 20) achievements.push({\n            id: 'trails_20',\n            name: 'Trail Enthusiast',\n            category: 'trails'\n        });\n        if (stats.totalTrails >= 5) achievements.push({\n            id: 'trails_5',\n            name: 'Trail Explorer',\n            category: 'trails'\n        });\n        // Special achievements\n        if (trails.some((t)=>t.difficulty === 'Expert')) achievements.push({\n            id: 'expert_trail',\n            name: 'Expert Climber',\n            category: 'special'\n        });\n        if (stats.nftsMinted >= 10) achievements.push({\n            id: 'nft_collector',\n            name: 'NFT Collector',\n            category: 'special'\n        });\n        return achievements;\n    }\n    // Export trail data\n    static exportTrailData(walletAddress) {\n        const data = {\n            recordings: this.getTrailRecordings(),\n            completedTrails: this.getCompletedTrails(walletAddress),\n            stats: this.getUserStats(walletAddress),\n            exportedAt: new Date().toISOString()\n        };\n        return JSON.stringify(data, null, 2);\n    }\n    // Import trail data\n    static importTrailData(jsonData) {\n        try {\n            const data = JSON.parse(jsonData);\n            if (data.recordings) {\n                localStorage.setItem(STORAGE_KEYS.TRAIL_RECORDINGS, JSON.stringify(data.recordings));\n            }\n            if (data.completedTrails) {\n                localStorage.setItem(STORAGE_KEYS.COMPLETED_TRAILS, JSON.stringify(data.completedTrails));\n            }\n            return true;\n        } catch (error) {\n            console.error('Failed to import trail data:', error);\n            return false;\n        }\n    }\n    // Clear all data\n    static clearAllData() {\n        try {\n            Object.values(STORAGE_KEYS).forEach((key)=>{\n                localStorage.removeItem(key);\n            });\n            // Clear user-specific stats\n            for(let i = 0; i < localStorage.length; i++){\n                const key = localStorage.key(i);\n                if (key === null || key === void 0 ? void 0 : key.startsWith(STORAGE_KEYS.USER_STATS)) {\n                    localStorage.removeItem(key);\n                }\n            }\n            return true;\n        } catch (error) {\n            console.error('Failed to clear data:', error);\n            return false;\n        }\n    }\n    // Get storage usage\n    static getStorageUsage() {\n        try {\n            let used = 0;\n            for(let i = 0; i < localStorage.length; i++){\n                const key = localStorage.key(i);\n                if (key === null || key === void 0 ? void 0 : key.startsWith('vintrek_')) {\n                    var _localStorage_getItem;\n                    used += ((_localStorage_getItem = localStorage.getItem(key)) === null || _localStorage_getItem === void 0 ? void 0 : _localStorage_getItem.length) || 0;\n                }\n            }\n            // Estimate total available (usually 5-10MB)\n            const total = 5 * 1024 * 1024 // 5MB estimate\n            ;\n            const percentage = used / total * 100;\n            return {\n                used,\n                total,\n                percentage\n            };\n        } catch (error) {\n            console.error('Failed to get storage usage:', error);\n            return {\n                used: 0,\n                total: 0,\n                percentage: 0\n            };\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/trailStorage.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/trailUtils.ts":
/*!*******************************!*\
  !*** ./src/lib/trailUtils.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateAverageSpeed: () => (/* binding */ calculateAverageSpeed),\n/* harmony export */   calculateDistance: () => (/* binding */ calculateDistance),\n/* harmony export */   calculateElevationGain: () => (/* binding */ calculateElevationGain),\n/* harmony export */   calculateElevationLoss: () => (/* binding */ calculateElevationLoss),\n/* harmony export */   calculateMaxSpeed: () => (/* binding */ calculateMaxSpeed),\n/* harmony export */   calculateTotalDistance: () => (/* binding */ calculateTotalDistance),\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance),\n/* harmony export */   formatDuration: () => (/* binding */ formatDuration),\n/* harmony export */   formatSpeed: () => (/* binding */ formatSpeed),\n/* harmony export */   generateTrailId: () => (/* binding */ generateTrailId),\n/* harmony export */   isValidCoordinate: () => (/* binding */ isValidCoordinate),\n/* harmony export */   isWithinRadius: () => (/* binding */ isWithinRadius),\n/* harmony export */   smoothCoordinates: () => (/* binding */ smoothCoordinates)\n/* harmony export */ });\n/**\n * Calculate distance between two GPS coordinates using Haversine formula\n * @param coord1 First coordinate\n * @param coord2 Second coordinate\n * @returns Distance in meters\n */ function calculateDistance(coord1, coord2) {\n    const R = 6371000 // Earth's radius in meters\n    ;\n    const φ1 = coord1.latitude * Math.PI / 180;\n    const φ2 = coord2.latitude * Math.PI / 180;\n    const Δφ = (coord2.latitude - coord1.latitude) * Math.PI / 180;\n    const Δλ = (coord2.longitude - coord1.longitude) * Math.PI / 180;\n    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) + Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n    return R * c;\n}\n/**\n * Calculate total distance for an array of coordinates\n * @param coordinates Array of GPS coordinates\n * @returns Total distance in meters\n */ function calculateTotalDistance(coordinates) {\n    if (coordinates.length < 2) return 0;\n    let totalDistance = 0;\n    for(let i = 1; i < coordinates.length; i++){\n        totalDistance += calculateDistance(coordinates[i - 1], coordinates[i]);\n    }\n    return totalDistance;\n}\n/**\n * Calculate elevation gain from coordinates\n * @param coordinates Array of GPS coordinates with altitude\n * @returns Elevation gain in meters\n */ function calculateElevationGain(coordinates) {\n    if (coordinates.length < 2) return 0;\n    let elevationGain = 0;\n    for(let i = 1; i < coordinates.length; i++){\n        const prev = coordinates[i - 1];\n        const curr = coordinates[i];\n        if (prev.altitude !== undefined && curr.altitude !== undefined) {\n            const diff = curr.altitude - prev.altitude;\n            if (diff > 0) {\n                elevationGain += diff;\n            }\n        }\n    }\n    return elevationGain;\n}\n/**\n * Calculate elevation loss from coordinates\n * @param coordinates Array of GPS coordinates with altitude\n * @returns Elevation loss in meters\n */ function calculateElevationLoss(coordinates) {\n    if (coordinates.length < 2) return 0;\n    let elevationLoss = 0;\n    for(let i = 1; i < coordinates.length; i++){\n        const prev = coordinates[i - 1];\n        const curr = coordinates[i];\n        if (prev.altitude !== undefined && curr.altitude !== undefined) {\n            const diff = prev.altitude - curr.altitude;\n            if (diff > 0) {\n                elevationLoss += diff;\n            }\n        }\n    }\n    return elevationLoss;\n}\n/**\n * Calculate average speed\n * @param distance Distance in meters\n * @param duration Duration in seconds\n * @returns Average speed in m/s\n */ function calculateAverageSpeed(distance, duration) {\n    if (duration === 0) return 0;\n    return distance / duration;\n}\n/**\n * Calculate maximum speed from coordinates\n * @param coordinates Array of GPS coordinates with timestamps\n * @returns Maximum speed in m/s\n */ function calculateMaxSpeed(coordinates) {\n    if (coordinates.length < 2) return 0;\n    let maxSpeed = 0;\n    for(let i = 1; i < coordinates.length; i++){\n        const prev = coordinates[i - 1];\n        const curr = coordinates[i];\n        const distance = calculateDistance(prev, curr);\n        const timeDiff = (new Date(curr.timestamp).getTime() - new Date(prev.timestamp).getTime()) / 1000;\n        if (timeDiff > 0) {\n            const speed = distance / timeDiff;\n            maxSpeed = Math.max(maxSpeed, speed);\n        }\n    }\n    return maxSpeed;\n}\n/**\n * Format distance with appropriate unit\n * @param meters Distance in meters\n * @param unit Target unit\n * @returns Formatted distance string\n */ function formatDistance(meters) {\n    let unit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'kilometers';\n    switch(unit){\n        case 'meters':\n            return \"\".concat(Math.round(meters), \" m\");\n        case 'kilometers':\n            return \"\".concat((meters / 1000).toFixed(2), \" km\");\n        case 'miles':\n            return \"\".concat((meters / 1609.34).toFixed(2), \" mi\");\n        default:\n            return \"\".concat(Math.round(meters), \" m\");\n    }\n}\n/**\n * Format duration in human-readable format\n * @param seconds Duration in seconds\n * @returns Formatted duration string\n */ function formatDuration(seconds) {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor(seconds % 3600 / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    if (hours > 0) {\n        return \"\".concat(hours, \"h \").concat(minutes, \"m \").concat(remainingSeconds, \"s\");\n    } else if (minutes > 0) {\n        return \"\".concat(minutes, \"m \").concat(remainingSeconds, \"s\");\n    } else {\n        return \"\".concat(remainingSeconds, \"s\");\n    }\n}\n/**\n * Format speed with appropriate unit\n * @param mps Speed in meters per second\n * @param unit Target unit\n * @returns Formatted speed string\n */ function formatSpeed(mps) {\n    let unit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'kmh';\n    switch(unit){\n        case 'mps':\n            return \"\".concat(mps.toFixed(2), \" m/s\");\n        case 'kmh':\n            return \"\".concat((mps * 3.6).toFixed(2), \" km/h\");\n        case 'mph':\n            return \"\".concat((mps * 2.237).toFixed(2), \" mph\");\n        default:\n            return \"\".concat(mps.toFixed(2), \" m/s\");\n    }\n}\n/**\n * Check if a coordinate is within a certain radius of a target\n * @param coord Current coordinate\n * @param target Target coordinate\n * @param radius Radius in meters\n * @returns True if within radius\n */ function isWithinRadius(coord, target, radius) {\n    const distance = calculateDistance(coord, target);\n    return distance <= radius;\n}\n/**\n * Generate a unique trail ID\n * @returns Unique trail ID\n */ function generateTrailId() {\n    return \"trail_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n}\n/**\n * Validate GPS coordinate\n * @param coord GPS coordinate to validate\n * @returns True if valid\n */ function isValidCoordinate(coord) {\n    return typeof coord.latitude === 'number' && typeof coord.longitude === 'number' && coord.latitude >= -90 && coord.latitude <= 90 && coord.longitude >= -180 && coord.longitude <= 180 && typeof coord.timestamp === 'string' && !isNaN(new Date(coord.timestamp).getTime());\n}\n/**\n * Smooth GPS coordinates by removing outliers\n * @param coordinates Array of GPS coordinates\n * @param maxSpeed Maximum reasonable speed in m/s (default: 20 m/s = 72 km/h)\n * @returns Filtered coordinates\n */ function smoothCoordinates(coordinates) {\n    let maxSpeed = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n    if (coordinates.length < 2) return coordinates;\n    const smoothed = [\n        coordinates[0]\n    ];\n    for(let i = 1; i < coordinates.length; i++){\n        const prev = smoothed[smoothed.length - 1];\n        const curr = coordinates[i];\n        const distance = calculateDistance(prev, curr);\n        const timeDiff = (new Date(curr.timestamp).getTime() - new Date(prev.timestamp).getTime()) / 1000;\n        if (timeDiff > 0) {\n            const speed = distance / timeDiff;\n            if (speed <= maxSpeed) {\n                smoothed.push(curr);\n            }\n        // Skip outlier points that indicate unrealistic speed\n        }\n    }\n    return smoothed;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/trailUtils.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);