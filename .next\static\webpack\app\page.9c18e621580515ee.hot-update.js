"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/wallet/WalletConnect.tsx":
/*!*************************************************!*\
  !*** ./src/components/wallet/WalletConnect.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletConnect: () => (/* binding */ WalletConnect)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Copy,ExternalLink,LogOut,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Copy,ExternalLink,LogOut,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Copy,ExternalLink,LogOut,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Copy,ExternalLink,LogOut,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Copy,ExternalLink,LogOut,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.mjs\");\n/* harmony import */ var _components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/WalletProvider */ \"(app-pages-browser)/./src/components/providers/WalletProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ WalletConnect auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction WalletConnect(param) {\n    let { onConnectionChange } = param;\n    _s();\n    const { connected, connecting, address, balance, connect, disconnect, getAvailableWallets } = (0,_components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__.useWallet)();\n    const [showWalletModal, setShowWalletModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAccountModal, setShowAccountModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const availableWallets = getAvailableWallets();\n    const handleConnect = async (walletName)=>{\n        try {\n            await connect(walletName);\n            setShowWalletModal(false);\n            onConnectionChange === null || onConnectionChange === void 0 ? void 0 : onConnectionChange(true);\n        } catch (error) {\n            console.error('Connection failed:', error);\n            alert('Failed to connect wallet. Please try again.');\n        }\n    };\n    const handleDisconnect = ()=>{\n        disconnect();\n        setShowAccountModal(false);\n        onConnectionChange === null || onConnectionChange === void 0 ? void 0 : onConnectionChange(false);\n    };\n    const copyAddress = ()=>{\n        if (address) {\n            navigator.clipboard.writeText(address);\n            alert('Address copied to clipboard!');\n        }\n    };\n    const formatAddress = (addr)=>{\n        return \"\".concat(addr.slice(0, 8), \"...\").concat(addr.slice(-8));\n    };\n    const formatBalance = (bal)=>{\n        try {\n            const lovelace = parseInt(bal);\n            const ada = lovelace / 1000000;\n            return \"\".concat(ada.toFixed(2), \" ADA\");\n        } catch (e) {\n            return '0.00 ADA';\n        }\n    };\n    const getWalletDisplayName = (walletName)=>{\n        const names = {\n            lace: 'Lace',\n            eternl: 'Eternl',\n            nami: 'Nami',\n            flint: 'Flint',\n            typhon: 'Typhon',\n            gerowallet: 'GeroWallet',\n            ccvault: 'ccvault.io',\n            yoroi: 'Yoroi'\n        };\n        return names[walletName] || walletName.charAt(0).toUpperCase() + walletName.slice(1);\n    };\n    const debugWalletInfo = ()=>{\n        console.log('=== Wallet Debug Info ===');\n        console.log('Available wallets:', availableWallets);\n        console.log('Window.cardano:',  true ? window.cardano : 0);\n        if ( true && window.cardano) {\n            console.log('Cardano object keys:', Object.keys(window.cardano));\n            Object.keys(window.cardano).forEach((key)=>{\n                console.log(\"\".concat(key, \":\"), window.cardano[key]);\n            });\n        }\n    };\n    if (connected && address) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setShowAccountModal(!showAccountModal),\n                    className: \"flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"hidden sm:inline\",\n                            children: formatAddress(address)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this),\n                showAccountModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border z-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Connected Wallet\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowAccountModal(false),\n                                            className: \"text-gray-400 hover:text-gray-600\",\n                                            children: \"\\xd7\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: balance ? formatBalance(balance) : 'Loading...'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Address\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-mono text-sm\",\n                                                children: formatAddress(address)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: copyAddress,\n                                                className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                title: \"Copy address\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.open(\"https://cardanoscan.io/address/\".concat(address), '_blank'),\n                                                className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                title: \"View on explorer\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleDisconnect,\n                                className: \"w-full flex items-center justify-center space-x-2 text-red-600 hover:bg-red-50 py-2 px-4 rounded-lg transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Disconnect\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setShowWalletModal(true),\n                disabled: connecting,\n                className: \"flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: connecting ? 'Connecting...' : 'Connect Wallet'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            showWalletModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"Connect Wallet\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowWalletModal(false),\n                                    className: \"text-gray-400 hover:text-gray-600\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 13\n                        }, this),\n                        availableWallets.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-12 w-12 text-gray-300 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"No Wallets Found\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Please install a Cardano wallet to continue.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://www.lace.io/\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"block w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors\",\n                                            children: \"Install Lace Wallet\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://eternl.io/\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"block w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors\",\n                                            children: \"Install Eternl Wallet\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>window.location.reload(),\n                                            className: \"block w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors\",\n                                            children: \"Refresh Page (After Installing Wallet)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Choose a wallet to connect to VinTrek:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 17\n                                }, this),\n                                availableWallets.map((walletName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleConnect(walletName),\n                                        disabled: connecting,\n                                        className: \"w-full flex items-center space-x-3 p-4 border rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium capitalize\",\n                                                        children: walletName\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            walletName === 'lace' && 'Lace Wallet',\n                                                            walletName === 'eternl' && 'Eternl Wallet',\n                                                            walletName === 'nami' && 'Nami Wallet',\n                                                            walletName === 'flint' && 'Flint Wallet'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, walletName, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 19\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                lineNumber: 172,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n_s(WalletConnect, \"3BB4weGfZX/q4DYQeyNMdzoJGDg=\", false, function() {\n    return [\n        _components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__.useWallet\n    ];\n});\n_c = WalletConnect;\nvar _c;\n$RefreshReg$(_c, \"WalletConnect\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/wallet/WalletConnect.tsx\n"));

/***/ })

});