// Dynamic imports for client-side only
let BrowserWallet: any = null
let Transaction: any = null
let AssetMetadata: any = null
let meshLoaded = false

const loadMeshSDK = async () => {
  if (typeof window !== 'undefined' && !meshLoaded) {
    try {
      const module = await import('@meshsdk/core')
      BrowserWallet = module.BrowserWallet
      Transaction = module.Transaction
      AssetMetadata = module.AssetMetadata
      meshLoaded = true
      console.log('Mesh SDK loaded successfully')
    } catch (error) {
      console.error('Failed to load Mesh SDK:', error)
    }
  }
}

// Initialize on client side
if (typeof window !== 'undefined') {
  loadMeshSDK()
}

// Blockchain configuration
export const BLOCKCHAIN_CONFIG = {
  network: process.env.NEXT_PUBLIC_CARDANO_NETWORK || 'testnet',
  blockfrostApiKey: process.env.NEXT_PUBLIC_BLOCKFROST_PROJECT_ID || '',
  blockfrostUrl: process.env.NEXT_PUBLIC_BLOCKFROST_API_URL || 'https://cardano-testnet.blockfrost.io/api/v0',
  nftPolicyId: process.env.NEXT_PUBLIC_NFT_POLICY_ID || '',
  tokenPolicyId: process.env.NEXT_PUBLIC_TOKEN_POLICY_ID || '',
  scriptAddress: process.env.NEXT_PUBLIC_SCRIPT_ADDRESS || '',
}

// Trail NFT metadata structure
export interface TrailNFTMetadata {
  name: string
  description: string
  image: string
  attributes: {
    trail_name: string
    location: string
    difficulty: string
    completion_date: string
    coordinates: string
    hiker_address: string
  }
}

// TREK Token configuration
export const TREK_TOKEN = {
  symbol: 'TREK',
  decimals: 6,
  policyId: BLOCKCHAIN_CONFIG.tokenPolicyId,
  assetName: '54524b', // 'TRK' in hex
}

// Blockchain service class
export class BlockchainService {
  private wallet: any | null = null

  constructor(wallet: any | null = null) {
    this.wallet = wallet
  }

  setWallet(wallet: any) {
    this.wallet = wallet
  }

  // Check if Mesh SDK is loaded and ready
  async ensureSDKLoaded(): Promise<boolean> {
    if (typeof window === 'undefined') return false

    if (!meshLoaded) {
      await loadMeshSDK()
    }

    return meshLoaded && BrowserWallet && Transaction && AssetMetadata
  }

  // Get wallet balance in ADA
  async getWalletBalance(): Promise<string> {
    if (!this.wallet || typeof window === 'undefined') throw new Error('Wallet not connected')

    const sdkReady = await this.ensureSDKLoaded()
    if (!sdkReady) throw new Error('Blockchain SDK not loaded')

    try {
      const balance = await this.wallet.getBalance()
      return balance
    } catch (error) {
      console.error('Error fetching wallet balance:', error)
      throw error
    }
  }

  // Get wallet assets (NFTs and tokens)
  async getWalletAssets(): Promise<any[]> {
    if (!this.wallet) throw new Error('Wallet not connected')
    
    try {
      const assets = await this.wallet.getAssets()
      return assets
    } catch (error) {
      console.error('Error fetching wallet assets:', error)
      throw error
    }
  }

  // Get TREK token balance
  async getTrekTokenBalance(): Promise<number> {
    try {
      const assets = await this.getWalletAssets()
      const trekAsset = assets.find(asset => 
        asset.unit.startsWith(TREK_TOKEN.policyId)
      )
      
      if (trekAsset) {
        return parseInt(trekAsset.quantity) / Math.pow(10, TREK_TOKEN.decimals)
      }
      
      return 0
    } catch (error) {
      console.error('Error fetching TREK token balance:', error)
      return 0
    }
  }

  // Get trail NFTs owned by wallet
  async getTrailNFTs(): Promise<any[]> {
    try {
      const assets = await this.getWalletAssets()
      const nfts = assets.filter(asset => 
        asset.unit.startsWith(BLOCKCHAIN_CONFIG.nftPolicyId) && 
        asset.quantity === '1'
      )
      
      // Fetch metadata for each NFT
      const nftsWithMetadata = await Promise.all(
        nfts.map(async (nft) => {
          try {
            const metadata = await this.fetchNFTMetadata(nft.unit)
            return { ...nft, metadata }
          } catch (error) {
            console.error(`Error fetching metadata for NFT ${nft.unit}:`, error)
            return { ...nft, metadata: null }
          }
        })
      )
      
      return nftsWithMetadata
    } catch (error) {
      console.error('Error fetching trail NFTs:', error)
      return []
    }
  }

  // Fetch NFT metadata from blockchain
  async fetchNFTMetadata(assetId: string): Promise<TrailNFTMetadata | null> {
    try {
      const response = await fetch(
        `${BLOCKCHAIN_CONFIG.blockfrostUrl}/assets/${assetId}`,
        {
          headers: {
            'project_id': BLOCKCHAIN_CONFIG.blockfrostApiKey,
          },
        }
      )
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const assetData = await response.json()
      
      if (assetData.onchain_metadata) {
        return assetData.onchain_metadata as TrailNFTMetadata
      }
      
      return null
    } catch (error) {
      console.error('Error fetching NFT metadata:', error)
      return null
    }
  }

  // Create booking transaction
  async createBookingTransaction(trailId: string, amount: number, date: string): Promise<string> {
    if (!this.wallet || typeof window === 'undefined' || !Transaction) throw new Error('Wallet not connected or Transaction not available')

    try {
      const walletAddress = await this.wallet.getChangeAddress()

      // Create booking metadata
      const bookingMetadata = {
        trail_id: trailId,
        booking_date: date,
        amount: amount,
        timestamp: new Date().toISOString(),
        hiker_address: walletAddress,
      }

      // Build transaction (simplified - in production, this would interact with smart contracts)
      const tx = new Transaction({ initiator: this.wallet })
      
      // Add metadata to transaction
      tx.setMetadata(674, bookingMetadata)
      
      // Send payment to script address (or trail operator)
      tx.sendLovelace(
        BLOCKCHAIN_CONFIG.scriptAddress,
        (amount * 1000000).toString() // Convert ADA to Lovelace
      )
      
      const unsignedTx = await tx.build()
      const signedTx = await this.wallet.signTx(unsignedTx)
      const txHash = await this.wallet.submitTx(signedTx)
      
      return txHash
    } catch (error) {
      console.error('Error creating booking transaction:', error)
      throw error
    }
  }

  // Mint trail completion NFT
  async mintTrailNFT(trailData: {
    trailName: string
    location: string
    difficulty: string
    coordinates: string
  }): Promise<string> {
    if (!this.wallet || typeof window === 'undefined' || !Transaction) throw new Error('Wallet not connected or Transaction not available')
    
    try {
      const walletAddress = await this.wallet.getChangeAddress()
      const completionDate = new Date().toISOString()
      
      // Create NFT metadata
      const metadata: TrailNFTMetadata = {
        name: `${trailData.trailName} Completion Certificate`,
        description: `Proof of completion for ${trailData.trailName} trail in ${trailData.location}`,
        image: `ipfs://QmTrailNFTImage${Date.now()}`, // In production, upload to IPFS
        attributes: {
          trail_name: trailData.trailName,
          location: trailData.location,
          difficulty: trailData.difficulty,
          completion_date: completionDate,
          coordinates: trailData.coordinates,
          hiker_address: walletAddress,
        }
      }
      
      // Generate unique asset name
      const assetName = `VinTrekNFT${Date.now()}`
      
      // Build minting transaction (simplified)
      const tx = new Transaction({ initiator: this.wallet })
      
      // Add minting logic here (requires smart contract integration)
      // This is a placeholder - actual implementation would use Mesh SDK's minting functions
      
      const unsignedTx = await tx.build()
      const signedTx = await this.wallet.signTx(unsignedTx)
      const txHash = await this.wallet.submitTx(signedTx)
      
      return txHash
    } catch (error) {
      console.error('Error minting trail NFT:', error)
      throw error
    }
  }

  // Mint TREK tokens as rewards
  async mintTrekTokens(amount: number, reason: string): Promise<string> {
    if (!this.wallet || typeof window === 'undefined' || !Transaction) throw new Error('Wallet not connected or Transaction not available')
    
    try {
      const walletAddress = await this.wallet.getChangeAddress()
      
      // Create reward metadata
      const rewardMetadata = {
        recipient: walletAddress,
        amount: amount,
        reason: reason,
        timestamp: new Date().toISOString(),
      }
      
      // Build token minting transaction (simplified)
      const tx = new Transaction({ initiator: this.wallet })
      
      // Add metadata
      tx.setMetadata(674, rewardMetadata)
      
      // Add token minting logic here (requires smart contract integration)
      
      const unsignedTx = await tx.build()
      const signedTx = await this.wallet.signTx(unsignedTx)
      const txHash = await this.wallet.submitTx(signedTx)
      
      return txHash
    } catch (error) {
      console.error('Error minting TREK tokens:', error)
      throw error
    }
  }

  // Verify transaction on blockchain
  async verifyTransaction(txHash: string): Promise<boolean> {
    try {
      const response = await fetch(
        `${BLOCKCHAIN_CONFIG.blockfrostUrl}/txs/${txHash}`,
        {
          headers: {
            'project_id': BLOCKCHAIN_CONFIG.blockfrostApiKey,
          },
        }
      )
      
      if (response.ok) {
        const txData = await response.json()
        return txData.block !== null // Transaction is confirmed if it's in a block
      }
      
      return false
    } catch (error) {
      console.error('Error verifying transaction:', error)
      return false
    }
  }
}

// Utility functions
export const formatLovelaceToAda = (lovelace: string): number => {
  return parseInt(lovelace) / 1000000
}

export const formatAdaToLovelace = (ada: number): string => {
  return (ada * 1000000).toString()
}

export const generateAssetName = (prefix: string): string => {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substr(2, 9)
  return `${prefix}${timestamp}${random}`
}

// Export singleton instance
export const blockchainService = new BlockchainService()
